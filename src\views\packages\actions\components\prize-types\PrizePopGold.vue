<template>
  <prize-pop-base :prize="prize" :prizeType="prizeType" :prizeTip="prizeTipText" v-on="$listeners">
    <template #content>
      <div class="prize-count">
        <span class="count">{{ prize.lotteryAmount || prize.amount }}</span>
        <span>金币</span>
      </div>
    </template>
  </prize-pop-base>
</template>

<script>
import { PrizeType as PrizeTypeEnum } from '@/enums';
import PrizePopBase from '../PrizePopBase.vue';

export default {
  name: 'PrizePopGold',
  components: {
    PrizePopBase
  },
  props: {
    prize: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      prizeType: PrizeTypeEnum.GOLD.valueOf()
    };
  },
  computed: {
    receiveDesc() {
      return '金币可兑好礼';
    },
    prizeTipText() {
      return '可去【我的-金币】查看';
    }
  }
};
</script>

<style lang="scss" scoped>
.prize-count {
  font-size: 12px;
  color: #ff273b;
  text-align: center;
  margin-bottom: 12px;

  .count {
    font-style: italic;
    font-weight: 400;
    font-size: 48px;
    color: #ff273b;
  }

  span:last-child {
    margin-left: 8px;
  }
}
</style>
