<template>
  <div
    class="lh-float-btn"
    :style="{
      width: width + 'px',
      height: height + 'px',
      left: left + 'px',
      top: top + 'px',
    }"
    ref="div"
    @click="onBtnClicked"
  >
    <slot></slot>
  </div>
</template>

<script>
import { debounce, throttle } from 'lodash';
import { onScrollerComponentScroll, offScrollerComponentScroll } from '@/bus';
export default {
  name: 'FloatImgBtn',
  props: {
    width: {
      type: Number,
      default: 60,
    },
    height: {
      type: Number,
      default: 60,
    },
    offsetTop: {
      type: Number,
      default: null,
    },
    offsetBottom: {
      type: Number,
      default: null,
    },
    gapWidth: {
      type: Number,
      default: 6,
    },
    // 默认距离顶部百分比
    coefficientHeight: {
      type: Number,
      default: 0.8,
    },
    hideOnScroll: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      timer: null,
      currentTop: 0,
      clientWidth: 0, // 视区宽
      clientHeight: 0,
      itemWidth: 0, // 浮动元素宽
      itemHeight: 0,
      left: 0,
      top: 0,
      scrollTimer: null,
      isScrolling: false,
    };
  },
  created() {
    this.clientWidth = document.documentElement.clientWidth;
    this.clientHeight = document.documentElement.clientHeight;
    this.left = this.clientWidth;
    if (this.offsetTop) {
      this.top = this.offsetTop;
    } else if (this.offsetBottom) {
      this.top = this.clientHeight - this.height - this.offsetBottom;
    } else if (this.coefficientHeight > 1) {
      this.top = this.clientHeight - this.height;
    } else {
      this.top = this.clientHeight * this.coefficientHeight;
    }
  },
  activated: function () {
    // 先解绑再绑定
    this.unbindOnScrollEvt();
    this.bindOnScrollEvt();
  },
  deactivated() {
    this.unbindOnScrollEvt();
  },
  mounted() {
    this.bindOnScrollEvt();
    this.itemWidth = this.$el.getBoundingClientRect().width;
    this.itemHeight = this.$el.getBoundingClientRect().height;
    this.left = this.clientWidth - this.itemWidth - this.gapWidth;

    this.$nextTick(() => {
      const div = this.$refs.div;
      div.addEventListener('touchstart', () => {
        div.style.transition = 'none';
      });
      div.addEventListener('touchmove', e => {
        e.preventDefault();
        if (e.targetTouches.length === 1) {
          let touch = e.targetTouches[0];
          this.left = touch.clientX - this.itemWidth / 2;
          this.top = touch.clientY - this.itemHeight / 2;
          // 检测上下滑出边界情况
          if (touch.clientY < this.itemHeight / 2) {
            // 上滑出
            this.top = 0;
          } else if (this.clientHeight - touch.clientY < this.itemHeight / 2) {
            // 下滑出
            this.top = this.clientHeight - this.itemHeight;
          }
          // 检测左右滑出边界情况
          if (touch.clientX < this.itemWidth / 2) {
            // 左滑出
            this.left = 0;
          } else if (this.clientWidth - touch.clientX < this.itemWidth / 2) {
            // 右滑出
            this.left = this.clientWidth - this.itemWidth;
          }
        }
      });
      div.addEventListener('touchend', () => {
        div.style.transition = 'all 0.3s';
        if (this.left > this.clientWidth / 2) {
          this.left = this.clientWidth - this.itemWidth - this.gapWidth;
        } else {
          this.left = this.gapWidth;
        }
        // 上下留出边距
        this.top = this.top === 0 ? this.gapWidth : this.top;
        this.top =
          this.top === this.clientHeight - this.itemHeight
            ? this.clientHeight - this.itemHeight - this.gapWidth
            : this.top;
      });
    });
  },
  // beforeDestroy() {
  //   this.unbindOnScrollEvt()
  // },
  methods: {
    onBtnClicked() {
      this.$emit('click');
    },
    // 绑定滚动隐藏事件
    bindOnScrollEvt() {
      // 是否开启滚动时隐藏
      if (this.hideOnScroll) {
        this.$nextTick(() => {
          const handleScroll = throttle(this.onScrollerScroll, 200);
          onScrollerComponentScroll(position => {
            handleScroll();
          });
          // const scrollContainer = document.getElementById('scroller');
          // 如果存在id="scroller"，就监听滚动div，否则就监听window
          window.addEventListener('scroll', handleScroll);
          // console.log('🚀 ~ file: FloatSideButton.vue:124 ~ beforeDestroy ~ hideOnScroll:', '绑定监听', this.$attrs.num)
        });
      }
    },
    // 取消绑定滚动隐藏事件
    unbindOnScrollEvt() {
      if (this.hideOnScroll) {
        // console.log('🚀 ~ file: FloatSideButton.vue:124 ~ beforeDestroy ~ hideOnScroll:', '卸载监听', this.$attrs.num)
        offScrollerComponentScroll();
        const handleScroll = throttle(this.onScrollerScroll, 200);
        window.removeEventListener('scroll', handleScroll);
      }
    },
    onScrollerScroll() {
      // 判断是否在滚动状态
      if (!this.isScrolling) {
        this.isScrolling = true;
        // 添加活动样式类名，触发移动效果
        this.$nextTick(() => {
          this.$el.classList.add('scrolling');
        });
      }

      // 停止滚动时恢复原来位置
      clearTimeout(this.scrollTimer);
      this.scrollTimer = setTimeout(() => {
        this.isScrolling = false;
        // 移除活动样式类名，恢复原来位置
        this.$el.classList.remove('scrolling');
      }, 300);
    },
  },
};
</script>

<style lang="less" scoped>
.lh-float-btn {
  // background:rgb(255,255,255);
  // box-shadow:0 2px 10px 0 rgba(0,0,0,0.1);
  border-radius: 50%;
  color: #666666;
  z-index: 20;
  transition: all 0.3s;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  position: fixed;
  bottom: 20vw;
  transition: all 0.3s ease;
  /* 添加过渡效果 */

  img {
    width: 50%;
    height: 50%;
    object-fit: contain;
    margin-bottom: 3px;
  }

  p {
    font-size: 7px;
  }
}

.lh-float-btn.scrolling {
  transform: translateX(70%);
  /* 将按钮移出屏幕 */
  opacity: 0.6;
}
</style>
