import APIModel from '@/api/APIModel';

/**
 * 接口文档：http://jgrm.net:10230/swagger-ui.html#/UGC%E5%86%85%E5%AE%B9%E7%AE%A1%E7%90%86
 */
const api = new APIModel({
  'box.config': '/Radio/box/config/detailAndJoinCount', // 查询盲盒抽奖配置信息接口
  'box.order': '/Radio/box/order/create', // 查询盲盒抽奖创建订单信息接口
  'box.detail': '/Radio/box/order/detail', // 查询盲盒抽奖订单详情信息接口
  'userUpload.list': '/Radio/linshiHelpData/userUpload/paging/list', // 附近上报信息
  'help.list': '/Radio/linshiHelpData/canHelp/paging/list', // 附近帮助信息
  'redEnvelope/action/info':
    '/Radio/consumption/season/redEnvelope/action/action/info', // 获取消费季抽奖活动详情
  'redEnvelope/action/awardServlet':
    '/Radio/consumption/season/redEnvelope/action/awardServlet', // 获取消费季抽奖活动抽奖
  'redEnvelope/action/floating/screen':
    '/Radio/consumption/season/redEnvelope/action/floating/screen/list', // 获取最新的飘屏记录
  'bankRights.info': '/Radio/bankRights/bankRightsAction/info/{id}', // 根据主键查询客户权益活动详情
  'bankRights.detail': '/Radio/bankRights/bankRights/info/{id}', // 根据主键查询客户权益详情
  'bankRights.judge': '/Radio/bankRights/bankRightsApplyRecord/judge', // 权益领取资格校验
  'bankRightsApply.add': '/Radio/bankRights/bankRightsApplyRecord/add', // 新增权益领取记录
  'bankRightsApply.list': '/Radio/bankRights/bankRightsApplyRecord/list', // 列表查询权益领取记录
  'bankManagerRights.list': '/Radio/bankRights/bankManagerRights/list', // 列表查询支行的权益
  'bankManagerRights.detail': '/Radio/bankRights/bankManagerRights/info/{id}', // 列表查询支行的权益详情
  'bankManagerRights.judge':
    '/Radio/bankRights/bankManagerRightsApplyRecord/judge', // 支行权益领取资格校验
  'bankManagerRights.add': '/Radio/bankRights/bankManagerRightsApplyRecord/add', // 新增支行的权益领取记录
  'bankManagerRightsApplyRecord/list':
    '/Radio/bankRights/bankManagerRightsApplyRecord/list', // 列表查询支行的权益领取记录
  'set/user/revieveinfo': '/Radio/act_lottery/set/user/revieveinfo', // 完善用户领取奖品的收货地址信息
  'keyword/CouponAction/detail': '/Radio/secretCodeGrantCouponAction/detail', // 对暗号领取优惠券活动详情
  'keyword/CouponAction/receive':
    '/Radio/secretCodeGrantCouponAction/grantCoupon', // 口令互动活动领取奖品
  'membership/grantMemberShipAction/detail':
    '/Radio/membership/grantMemberShipAction/detail', // 查询赠送会员活动详情
  'membership/grantMemberShipAction/receive':
    '/Radio/membership/grantMemberShipAction/receive', // 用户领取会员接口
  'grantSecondaryCardAction/detail': '/app/car/grantSecondaryCardAction/detail', // 赠送次卡活动详情
  'grantSecondaryCardAction/receive': '/app/car/grantMemberShipAction/receive', // 用户领取次卡接口
  'sendBlessing/detail': '/Radio/sendBlessingActivity/{id}/for/app', // 获取活动详情
  'sendBlessing/draw': '/Radio/sendBlessingActivity/{id}/draw', // 抽祝福卡
  'sendBlessing/lottery/draw': '/Radio/sendBlessingActivity/{id}/lottery/draw', // 抽奖接口
  'sendBlessing/win/records': '/Radio/sendBlessingActivity/{id}/win/records', // 中奖记录接口
  'sendBlessing/user/revieveinfos':
    '/Radio/sendBlessingActivity/win/record/set/user/revieveinfo', // 完善用户领取奖品的收货地址信息
});

export function getBoxConfig(params) {
  return api.doGet('box.config', params);
}
export function getBoxOrder(params) {
  return api.doGet('box.order', params);
}
export function getBoxDetail(params) {
  return api.doGet('box.detail', params);
}

export function fetchReportData(params) {
  return api.postJSON('userUpload.list', params);
}
export function fetchHelpData(params) {
  return api.postJSON('help.list', params);
}

export function getGashaponDetail(params) {
  return api.doGet('redEnvelope/action/info', params);
}

export function gashaponDraw(params) {
  return api.doPost('redEnvelope/action/awardServlet', params);
}

export function getGashaponFloatingMsg(params) {
  return api.doGet('redEnvelope/action/floating/screen', params);
}

export function getBankRightsInfo(params) {
  const url = api.render('bankRights.info', { id: params.id });
  return api.doGet(url);
}

export function getBankRightsDetail(params) {
  const url = api.render('bankRights.detail', { id: params.id });
  return api.doGet(url);
}

export function bankRightsJudge(params) {
  return api.doPost('bankRights.judge', params);
}

export function bankRightsApply(params) {
  return api.postJSON('bankRightsApply.add', params);
}

export function bankRightsList(params) {
  return api.postJSON('bankRightsApply.list', params);
}

export function getBankRightsListBank(params) {
  const url = api.render('bankManagerRights.list');
  return api.postJSON(url, params);
}

export function getBankRightsDetailBank(params) {
  const url = api.render('bankManagerRights.detail', { id: params.id });
  return api.doGet(url);
}

export function bankRightsJudgeBank(params) {
  return api.doPost('bankManagerRights.judge', params);
}

export function bankRightsApplyBank(params) {
  return api.postJSON('bankManagerRights.add', params);
}

export function bankRightsListBank(params) {
  return api.postJSON('bankManagerRightsApplyRecord/list', params);
}

/**
 * 领取会员活动的奖项
 * @param {string} id 礼品领取记录id
 * @param {string} address 礼品领取记录id
 * @param {string} phone 礼品领取记录id
 * @param {string} postareaProv 礼品领取记录id
 * @param {string} postareaCity 礼品领取记录id
 * @param {string} postareaCountry 礼品领取记录id
 * @param {string} recvName 礼品领取记录id
 */
export function setPrizeRecieveAddress(params) {
  const url = api.render('set/user/revieveinfo');
  return api.postJSON(url, params);
}

export function getKeywordCouponAction(id) {
  const url = api.render('keyword/CouponAction/detail');
  return api.doGet(url, { id });
}
export function receiveAward(params) {
  return api.doPost('keyword/CouponAction/receive', params);
}

/**
 * @description: 查询赠送会员活动详情
 * @param {string} id 活动id
 */
export function getGrantMemberShipActionDetail(id) {
  const url = api.render('membership/grantMemberShipAction/detail');
  return api.doGet(url, { id });
}
/**
 * @description: 用户领取会员接口
 * @param {string} id 活动id
 * @param {string} memberCategory 会员类型
 */
export function receiveMemberShip(params) {
  return api.doPost('membership/grantMemberShipAction/receive', params);
}

export function getGrantSecondaryCardActionDetail(id) {
  const url = api.render('grantSecondaryCardAction/detail');
  return api.doGet(url, { id });
}

export function receiveSecondaryCard(params) {
  return api.doPost('grantSecondaryCardAction/receive', params);
}

export function getSendBlessingDetail(id) {
  const url = api.render('sendBlessing/detail', { id });
  return api.doGet(url);
}

export function drawSendBlessing(id) {
  const url = api.render('sendBlessing/draw', { id });
  return api.doPost(url);
}

export function lotterySendBlessing(id) {
  const url = api.render('sendBlessing/lottery/draw', { id });
  return api.doPost(url);
}
export function getSendBlessingWinRecords(id, params) {
  const url = api.render('sendBlessing/win/records', { id });
  return api.doGet(url, params);
}

export function setSendBlessingUserRevieveinfo(params) {
  const url = api.render('sendBlessing/user/revieveinfos');
  return api.postJSON(url, params);
}
