import { handleError } from '../error-handler';

export default [
  {
    path: '/blindBox/use',
    name: '盲盒红包使用方法',
    component: resolve => {
      import(
        /* webpackChunkName: "jglh-actions" */ '@/views/packages/actions/rule-rich-text.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/blindBox/:id',
    name: '盲盒抽奖',
    component: resolve => {
      import(
        /* webpackChunkName: "jglh-actions" */ '@/views/packages/actions/blind-box.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/activity/topic/:id',
    name: '活动专题',
    component: resolve => {
      import(
        /* webpackChunkName: "jglh-actions" */ '@/views/packages/actions/ActivityTopic.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/feature/index',
    name: '车生活服务专区',
    component: resolve => {
      import(
        /* webpackChunkName: "jglh-actions" */ '@/views/packages/actions/feature-show.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/gourmet/index',
    name: '一品天下美食',
    component: resolve => {
      import(
        /* webpackChunkName: "jglh-actions" */ '@/views/packages/actions/gourmet-world.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/gourmet/detail',
    name: '一品天下美食详情',
    component: resolve => {
      import(
        /* webpackChunkName: "jglh-actions" */ '@/views/packages/actions/gourmet-detail.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/disaster/report',
    name: '灾害上报',
    component: resolve => {
      import(
        /* webpackChunkName: "jglh-actions" */ '@/views/packages/actions/disaster-report.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/gashapon',
    name: '扭蛋机',
    component: resolve => {
      import(
        /* webpackChunkName: "jglh-actions" */ '@/views/packages/actions/gashapon.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/benefits/zyyh',
    name: 'BenefitsZone',
    component: resolve => {
      import(
        /* webpackChunkName: "jglh-actions" */ '@/views/packages/actions/benefits-zyyh.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/benefits/zyyh/detail',
    name: 'BenefitsDetail',
    component: resolve => {
      import(
        /* webpackChunkName: "jglh-actions" */ '@/views/packages/actions/benefits-detail.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/benefits/zyyh/record',
    name: 'BenefitsRecord',
    component: resolve => {
      import(
        /* webpackChunkName: "jglh-actions" */ '@/views/packages/actions/benefits-record.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/benefits/zyyh/form',
    name: 'BenefitsForm',
    component: resolve => {
      import(
        /* webpackChunkName: "jglh-actions" */ '@/views/packages/actions/benefits-form.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/benefits/zyyh/bank',
    name: 'BenefitsZoneBank',
    component: resolve => {
      import(
        /* webpackChunkName: "jglh-actions" */ '@/views/packages/actions/benefits-zyyh-bank.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/prize/address/submit',
    name: 'PrizeAddressPage',
    component: resolve => {
      import(
        /* webpackChunkName: "jglh-actions" */ '@/views/packages/actions/prize-address-page.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/activity/keyword',
    name: 'KeywordActivity',
    component: resolve => {
      import(
        /* webpackChunkName: "jglh-actions" */ '@/views/packages/actions/keyword-activity.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/member/experience',
    name: 'MemberExperience',
    component: resolve => {
      import(
        /* webpackChunkName: "jglh-actions" */ '@/views/packages/actions/member-experience.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/consumer/benefits',
    name: 'ConsumerBenefits',
    component: resolve => {
      import(
        /* webpackChunkName: "jglh-actions" */ '@/views/packages/actions/consumer-benefits.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/may/travel',
    name: 'MayTravelZone',
    component: resolve => {
      import(
        /* webpackChunkName: "jglh-actions" */ '@/views/packages/actions/may-travel-zone.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/card/bless',
    name: 'CardCollection',
    component: resolve => {
      import(
        /* webpackChunkName: "jglh-actions" */ '@/views/packages/actions/cardCollection.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/prize/records',
    name: 'PrizeRecords',
    component: resolve => {
      import(
        /* webpackChunkName: "jglh-actions" */ '@/views/packages/actions/prizeRecords.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  // {
  //   path: '/grid',
  //   name: '九宫格抽奖',
  //   component: resolve => {
  //     import(/* webpackChunkName: "jglh-actions" */ '@/views/packages/actions/grid.vue').then(resolve).catch(handleError);
  //   },
  // },
];
