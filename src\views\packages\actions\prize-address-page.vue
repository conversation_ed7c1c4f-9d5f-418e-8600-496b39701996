<template>
  <container class="theme-white vip" @ready="init" @leave="onLeave" @resume="onResume" :keep-alive="keepAlive">
    <x-header title="收货地址">
      <x-button slot="left" type="back"></x-button>
    </x-header>

    <content-view ref="content" :status="status" @reload="reload">
      <template v-if="status == AppStatus.READY">
        <div class="wrap">
          <!-- <div class="goods-block">
            <p class="block-title">您选中的礼品</p>
            <div class="goods">
              <c-picture class="goods-img" :src="goods.pic"></c-picture>
              <div class="info">
                <p class="title">{{ goods.title }}</p>
                <p class="desc">{{ goods.descInfo }}</p>
              </div>
            </div>
          </div> -->
          <div class="address-block">
            <p class="block-title">礼品寄送地址</p>
            <biz-address-picker class="address-picker" :class="{ 'address-picket-empty': !addressModel }"
              v-model="addressModel" :defaultAddress="defaultAddress" :disabled="!!hasRecvAddress"
              placeholder="请选择收货地址">
            </biz-address-picker>
          </div>
          <div v-if="
            joinRemark.postNo != 'undefined' &&
            joinRemark.postNo != undefined &&
            joinRemark.postNo
          " class="goods-block deliver">
            <p class="block-title">配送信息</p>
            <div class="row">
              <div class="label">运输公司</div>
              <div class="value">{{ joinRemark.postCompanyName }}</div>
            </div>
            <div class="row">
              <div class="label">快递单号</div>
              <div class="value">{{ joinRemark.postNo }}</div>
              <div class="right" @click="toCopy">复制</div>
            </div>
          </div>
        </div>
        <div v-if="!hasRecvAddress" class="block block-bottom" slot="foot">
          <van-button class="buy-btn" color="#FD4925" round block type="primary" :loading="loading"
            loading-text="提交中..." :disabled="!addressModel || disabledBtn" @click="submit">提交收货地址</van-button>
        </div>
      </template>
    </content-view>
  </container>
</template>

<script>
import { formatMoney } from '@/utils';
import { getUserInfo } from '@/api';
import { AppStatus, OrderBizType } from '@/enums';
import { mixinAuthRouter, mixinShare, mixinOrder } from '@/mixins';
import { isInJglh, isInWeixin } from '@/common/env';
import { loading, dialog, toast, back } from '@/bus';
import { setPrizeRecieveAddress, setSendBlessingUserRevieveinfo } from './api';
import BizAddressPicker from '@/views/components/BizAddressPicker.vue';
import { Button, Icon, Toast, Slider } from 'vant';

const ACTIVITY_TYPE = {
  SEND_BLESSING: 'sendBlessing',
};
export default {
  name: 'PrizeAddressPage',
  components: {
    BizAddressPicker,
    [Button.name]: Button,
    [Icon.name]: Icon,
    [Toast.name]: Toast,
    [Slider.name]: Slider,
  },
  mixins: [mixinAuthRouter, mixinShare, mixinOrder],
  data() {
    const defaultAddress = this.$route.query.address; // 默认收货地址id
    const receiveType = this.$route.query.type; // 实物礼包领取方式 1-列表领取，2-盲盒领取
    const action = this.$route.query.action; // 活动类型
    const recordId = this.$route.query.recordId; // 中奖记录id
    return {
      AppStatus,
      receiveType,
      action,
      recordId,
      status: AppStatus.LOADING,
      addressModel: null,
      defaultAddress,
      goods: {},
      loading: false,
      disabledBtn: false,
    };
  },
  computed: {
    // hasDeliver
    hasRecvAddress() {
      return (
        this.goods.joinRemark &&
        (this.goods.joinRemark.phone || this.goods.joinRemark.postNo) &&
        this.goods.joinRemark.phone != undefined &&
        this.goods.joinRemark.phone != 'undefined'
      );
    },
    joinRemark() {
      return this.goods.joinRemark || {};
    },
  },
  mounted() { },
  methods: {
    ...{ formatMoney },
    init() {
      let params = this.$route.query;
      this.goods = {
        joinRemark: {
          postNo: params.postNo,
          postCompanyName: params.postCompanyName,
          phone: params.phone,
        },
      };
      this.status = AppStatus.READY;
    },
    submit() {
      // 两种情况
      this.loading = true;
      let fetchFn = null;
      if (this.action === ACTIVITY_TYPE.SEND_BLESSING) {
        fetchFn = setSendBlessingUserRevieveinfo;
      } else {
        fetchFn = setPrizeRecieveAddress;
      }
      fetchFn({
        id: this.recordId, // 记录id
        address: this.addressModel.address,
        phone: this.addressModel.phone,
        postareaCity: this.addressModel.city,
        postareaCountry: this.addressModel.county,
        postareaProv: this.addressModel.province,
        recvName: this.addressModel.recvName,
      })
        .then(res => {
          // 领取成功后后退
          Toast('提交成功');
          this.loading = false;
          this.disabledBtn = true;
          setTimeout(() => {
            back();
          }, 1500);
        })
        .catch(e => {
          this.loading = false;
          e && dialog().alert(e);
        });
    },
    toCopy() {
      let selection = window.getSelection();
      let range = document.createRange();
      let referenceNode = document.createElement('div');
      referenceNode.innerHTML = this.joinRemark.postNo;
      document.body.appendChild(referenceNode);
      range.selectNodeContents(referenceNode);
      selection.removeAllRanges();
      selection.addRange(range);
      let ret = document.execCommand('copy');
      document.body.removeChild(referenceNode);

      if (ret) {
        Toast('复制成功');
      }
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    onResume() {
      this.init();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
  },
};
</script>

<style lang="scss" scoped>
.wrap {
  background: #f3f3f3;
  padding: 15px;
}

.goods-block {
  background: #ffffff;
  border-radius: 10px;
  padding: 15px;
  line-height: 1;
}

.block-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  padding-bottom: 15px;
}

.deliver {
  margin-top: 15px;
}

.row {
  display: flex;
  align-items: center;

  &:not(:last-child) {
    margin-bottom: 20px;
  }

  .label {
    font-size: 15px;
    color: #666666;
    width: 80px;
  }

  .value {
    flex: 1;
    font-size: 16px;
    font-weight: bold;
    color: #333333;
  }

  .right {
    font-size: 12px;
    color: #fd4925;
  }
}

.address-block {
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-sizing: border-box;
  padding: 10px;
  // margin-top: 15px;
}

.address-picker {
  margin-right: 20px;
  text-align: left;
  padding-left: 30px;
  background: url(~@pkg/mall/image/address.png) left center no-repeat;
  background-size: 20px;

  &.address-picket-empty {
    // padding-left: 0;
    // background: none;
    // padding: 10px 0;
    color: gray;
  }

  .address-detail {
    white-space: initial;
    text-overflow: initial;
    overflow: initial;
  }
}

.goods {
  background: #fff;
  border-radius: 10px;
  display: flex;
  align-items: center;

  &:not(:last-child) {
    margin-bottom: 10px;
  }

  .goods-img {
    width: 60px;
    height: 60px;
    border-radius: 5px;
    margin-right: 10px;
    overflow: hidden;
  }

  .info {
    flex: 1;
    width: 0;
    display: inline-flex;
    flex-direction: column;
    justify-content: center;

    .title {
      font-size: 15px;
      color: #333333;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      margin-bottom: 5px;
    }

    .desc {
      font-size: 12px;
      color: #999999;
      margin-bottom: 10px;
    }
  }

  .btn {
    width: 50px;
    height: 50px;
    background: #fd4925;
    border-radius: 50%;
    text-align: center;
    font-size: 13px;
    font-weight: bold;
    color: #ffffff;
    display: inline-flex;
    align-items: center;
    justify-content: center;

    &.warning {
      background: #fddbc4;
      color: #ffffff;
    }

    &.disabled {
      background: #dddddd;
      color: #ffffff;
    }
  }
}

.tips {
  margin-top: 10px;
  font-size: 10px;
  color: #999999;
}

.slide ::v-deep {
  display: flex;
  align-items: center;

  .van-slider {
    flex: 1;
  }

  .surplus {
    width: 56px;
    text-align: right;
    font-size: 10px;
    color: #666666;
  }
}

::v-deep .van-slider--disabled {
  opacity: 1;
}

::v-deep .van-slider__bar {
  background: linear-gradient(90deg, #ff7c63, #fd4925);
  max-width: 100% !important;
}

.block-bottom {
  padding: 20px 15px;
  background: #f3f3f3;

  .buy-btn {
    color: #ffffff !important;

    .van-button__text {
      font-size: 18px;
      font-weight: bold;
    }
  }
}
</style>
