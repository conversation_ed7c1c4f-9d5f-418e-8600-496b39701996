<template>
  <van-dialog
    v-model="visible"
    title="快递单号确认"
    :show-cancel-button="true"
    :show-confirm-button="false"
    :close-on-click-overlay="false"
    class="ocr-result-dialog"
  >
    <div class="ocr-content">
      <!-- 识别成功状态 -->
      <div v-if="ocrResult.success" class="success-content">
        <div class="result-header">
          <van-icon name="checked" color="#07c160" size="20" />
          <span class="success-text">识别成功</span>
        </div>

        <div class="result-section">
          <div class="section-title">
            识别到的快递单号：<span class="edit-hint">(可编辑)</span>
          </div>
          <van-field
            v-model="editableTrackingNumber"
            placeholder="点击此处可直接编辑快递单号"
            :border="true"
            @input="onTrackingNumberChange"
          >
            <template #right-icon>
              <van-icon name="edit" size="16" color="#fd4925" />
            </template>
          </van-field>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <van-button type="default" size="small" @click="retryUpload">
            重新识别
          </van-button>
          <van-button type="primary" size="small" @click="confirmResult">
            确认使用
          </van-button>
        </div>
      </div>

      <!-- 识别失败状态 -->
      <div v-else class="error-content">
        <div class="error-header">
          <van-icon name="close" color="#ee0a24" size="20" />
          <span class="error-text">识别失败</span>
        </div>

        <div class="error-message">
          <div class="error-title">{{ getErrorTitle() }}</div>
          <div class="error-description">{{ getErrorDescription() }}</div>
        </div>

        <!-- 解决方案 -->
        <div class="solution-section">
          <div class="section-title">建议解决方案：</div>
          <div class="solution-list">
            <div
              v-for="(solution, index) in getSolutions()"
              :key="index"
              class="solution-item"
            >
              <van-icon name="arrow" size="12" />
              <span>{{ solution }}</span>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <van-button type="default" size="small" @click="retryUpload">
            重新识别
          </van-button>
          <van-button type="primary" size="small" @click="manualInput">
            手动输入
          </van-button>
        </div>
      </div>
    </div>
  </van-dialog>
</template>

<script>
import { Dialog, Field, Button, Icon, Tag } from 'vant';

export default {
  name: 'OcrResultDialog',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Field.name]: Field,
    [Button.name]: Button,
    [Icon.name]: Icon,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    ocrResult: {
      type: Object,
      default: () => ({
        success: false,
        trackingNumber: '',
        errorType: '',
        errorMessage: '',
      }),
    },
  },
  data() {
    return {
      visible: this.value,
      editableTrackingNumber: '',
    };
  },
  watch: {
    value(val) {
      this.visible = val;
      if (val && this.ocrResult.success) {
        this.editableTrackingNumber = this.ocrResult.trackingNumber || '';
      }
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  methods: {
    // 快递单号输入变化处理
    onTrackingNumberChange() {
      // 简单的非空验证
      // 可以根据需要添加更多验证逻辑
    },

    // 获取错误标题
    getErrorTitle() {
      const errorType = this.ocrResult.errorType;
      const errorTitles = {
        IMAGE_QUALITY: '图片质量问题',
        FORMAT_ERROR: '图片格式不支持',
        NETWORK_ERROR: '网络连接异常',
        OCR_ENGINE_ERROR: '识别引擎异常',
        NO_TEXT_FOUND: '未识别到文字信息',
      };
      return errorTitles[errorType] || '识别失败';
    },

    // 获取错误描述
    getErrorDescription() {
      const errorType = this.ocrResult.errorType;
      const descriptions = {
        IMAGE_QUALITY: '图片可能存在模糊、光线不足或角度倾斜等问题',
        FORMAT_ERROR: '当前图片格式不支持，请使用JPG、PNG等常见格式',
        NETWORK_ERROR: '网络连接不稳定，请检查网络后重试',
        OCR_ENGINE_ERROR: '识别服务暂时不可用，请稍后重试',
        NO_TEXT_FOUND: '图片中未找到清晰的快递单号信息',
      };
      return (
        descriptions[errorType] ||
        this.ocrResult.errorMessage ||
        '请重新尝试或手动输入快递单号'
      );
    },

    // 获取解决方案
    getSolutions() {
      const errorType = this.ocrResult.errorType;
      const solutions = {
        IMAGE_QUALITY: [
          '确保图片清晰，避免模糊',
          '保证光线充足，避免阴影',
          '将快递单平放，避免倾斜',
          '确保快递单号完整可见',
        ],
        FORMAT_ERROR: ['使用JPG或PNG格式的图片', '确保图片文件完整未损坏'],
        NETWORK_ERROR: [
          '检查网络连接是否正常',
          '切换到稳定的网络环境',
          '稍后重新尝试上传',
        ],
        OCR_ENGINE_ERROR: ['稍后重新尝试识别', '如问题持续，请联系客服'],
        NO_TEXT_FOUND: [
          '确保拍摄的是快递面单',
          '确保快递单号区域清晰可见',
          '尝试重新拍摄或选择其他图片',
        ],
      };
      return (
        solutions[errorType] || [
          '检查图片质量和格式',
          '确保网络连接正常',
          '重新拍摄或手动输入',
        ]
      );
    },

    // 确认识别结果
    confirmResult() {
      if (!this.editableTrackingNumber.trim()) {
        this.$toast('请输入快递单号');
        return;
      }

      this.$emit('confirm', {
        trackingNumber: this.editableTrackingNumber.trim(),
      });
      this.visible = false;
    },

    // 重新识别
    retryUpload() {
      this.$emit('retry-upload');
      this.visible = false;
    },

    // 手动输入
    manualInput() {
      this.$emit('manual-input');
      this.visible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.ocr-result-dialog ::v-deep {
  .van-dialog__content {
    padding: 0 16px;
  }

  .van-dialog__header {
    padding: 16px 0;
    font-weight: 600;
    font-size: 16px;
  }

  .ocr-content {
    padding: 8px 0 16px;
  }

  .success-content,
  .error-content {
    text-align: left;
  }

  .result-header,
  .error-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .success-text {
      color: #07c160;
      font-weight: 500;
      margin-left: 8px;
    }

    .error-text {
      color: #ee0a24;
      font-weight: 500;
      margin-left: 8px;
    }
  }

  .result-section {
    margin-bottom: 16px;

    .section-title {
      font-size: 14px;
      color: #323233;
      margin-bottom: 8px;
      font-weight: 500;

      .edit-hint {
        color: #fd4925;
        font-size: 12px;
        font-weight: normal;
        margin-left: 4px;
      }
    }

    // 自定义输入框样式
    .van-field {
      border: none !important;
      border-radius: 4px;
      transition: all 0.3s;
      background-color: #f5f5f5;
      box-shadow: none !important;

      &:focus-within {
        border-color: #fd4925;
        box-shadow: 0 0 0 2px rgba(253, 73, 37, 0.1);
      }
    }
  }

  .error-message {
    margin-bottom: 16px;

    .error-title {
      font-size: 14px;
      color: #323233;
      font-weight: 500;
      margin-bottom: 4px;
    }

    .error-description {
      font-size: 12px;
      color: #646566;
      line-height: 1.4;
    }
  }

  .solution-section {
    margin-bottom: 16px;

    .section-title {
      font-size: 14px;
      color: #323233;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .solution-list {
      .solution-item {
        display: flex;
        align-items: flex-start;
        gap: 6px;
        margin-bottom: 6px;

        span {
          font-size: 12px;
          color: #646566;
          line-height: 1.4;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 20px;

    .van-button {
      flex: 1;
      height: 42px;
    }
    .van-button--default {
      background-color: #f5f5f5;
      border-color: #f5f5f5;
    }
    .van-button--primary {
      background-color: #fd4925;
      border-color: #fd4925;

      &:active {
        background-color: #e64321;
        border-color: #e64321;
      }
    }
  }
}
</style>
