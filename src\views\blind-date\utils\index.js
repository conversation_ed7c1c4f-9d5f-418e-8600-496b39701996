// 按照年龄筛选用户
export function filterUsersByAge(users, minAge, maxAge) {
  if (!minAge && !maxAge) return users;

  return users.filter(user => {
    const age = parseInt(user.age, 10);
    if (minAge && maxAge) {
      return age >= minAge && age <= maxAge;
    } else if (minAge) {
      return age >= minAge;
    } else if (maxAge) {
      return age <= maxAge;
    }
    return true;
  });
}

// 按照城市筛选用户
export function filterUsersByCity(users, city) {
  if (!city) return users;
  return users.filter(user => user.city === city);
}

// 按照最新注册时间排序
export function sortUsersByNewest(users) {
  return [...users].sort((a, b) => {
    return new Date(b.registerTime) - new Date(a.registerTime);
  });
}

// 按照年龄相近程度排序（基于当前用户的年龄）
export function sortUsersBySimilarAge(users, currentUserAge) {
  if (!currentUserAge) return users;

  return [...users].sort((a, b) => {
    const ageA = parseInt(a.age, 10);
    const ageB = parseInt(b.age, 10);
    return Math.abs(ageA - currentUserAge) - Math.abs(ageB - currentUserAge);
  });
}

// 格式化时间
export function formatDate(timestamp, format = 'YYYY-MM-DD') {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  return format.replace('YYYY', year).replace('MM', month).replace('DD', day);
}

// 防抖函数
export function debounce(fn, delay = 300) {
  let timer = null;
  return function (...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
}

// 将用户数据转换为显示格式
export function formatUserData(user) {
  return {
    ...user,
    displayAge: `${user.age}岁`,
    displayLocation: `${user.city} | ${user.industry}`,
  };
}
