<template>
  <container class="vehicle-inspection-online" @ready="init" @leave="onLeave" @resume="onResume"
    :keep-alive="keepAlive">
    <x-header :title="pageTitle">
      <x-button slot="left" type="back"></x-button>
    </x-header>
    <content-view ref="content" :status="status" @reload="reload">
      <div class="container_box">
        <biz-rich-text class="rules-desc" :value="description"></biz-rich-text>
      </div>
    </content-view>
  </container>
</template>

<script>
import { isInJglh } from '@/common/env';
import { getVersion } from '@/bridge';
import { AppStatus } from '@/enums';
// import { mixinAuthRouter, mixinShare } from '@/mixins';
import { dialog, loading } from '@/bus';
import BizRichText from '@/views/components/BizRichText.vue';

export default {
  name: 'BlindBoxUse',
  // mixins: [mixinAuthRouter, mixinShare],
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      keepAlive: true,
      title: '红包使用方法',
      description: '', // 活动规则
    };
  },
  components: {
    BizRichText,
  },
  computed: { // 计算属性
    pageTitle() {
      if (this.$route.query.title) {
        return this.$route.query.title
      }
      return this.title;
    },
  },
  mounted() { // 元素挂载结束
    // 取本地存储的红包使用规则
    const ruleRichTextStr = localStorage.getItem('rule_rich_text')
    if (ruleRichTextStr) {
      this.description = ruleRichTextStr
    }
  },
  methods: { // 事件方法
    initPageData: function () { // 获取页面加载的数据
      this.status = AppStatus.READY;
    },
    init() {
      this.initPageData();
    },
    reload() {
      this.status = AppStatus.LOADING;
      // 重新加载页面
      this.init();
    },
    onResume() {
      // 页面重新激活时在重新获取数据
      this.initPageData();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
  }
}
</script>
<style lang="scss" scoped>
@import '~styles/mixin/index.scss';

.container {
  // background: #ffd087;
}

.container_box {
  position: relative;
}
</style>
