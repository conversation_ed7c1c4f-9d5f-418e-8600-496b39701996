<template>
  <!-- 页面包裹组件，处理初始化、前后台切换等状态 -->
  <container @ready="onReady" @leave="onLeave" @resume="onResume" @init="init">
    <!-- 头部导航 -->
    <x-header ref="header" :title="pageTitle">
      <x-button slot="left" type="back"></x-button>
      <div slot="right" class="header-right">
        <div class="share-btn" @click="share('show')">
          <i class="icon_jglh icon-fenxiang1"></i>
        </div>
      </div>
    </x-header>
    <!-- 页面内容包裹组件 -->
    <content-view ref="content" :status="status" @reload="reload">
      <template v-if="status == AppStatus.READY">
        <div class="cp-detail-container">
          <!-- 顶部背景装饰 -->
          <div class="top-background" id="heartsContainer">
            <div class="header-content">
              <h1>心动时刻</h1>
              <p>遇见彼此, 是美好的开始</p>
            </div>
          </div>

          <!-- CP照片区域 -->
          <div class="cp-photos-section">
            <div class="photos-container">
              <div class="photo-card" @click="goUserDetail(leftUser.id)">
                <!-- <img :src="getImageUrl(leftUser.profileImages)" alt="生活照" class="user-photo"> -->
                <c-picture class="user-photo" :src="getImageUrl(leftUser.profileImages)" :type="`?imageView2/1/w/480/h/600/format/${supportWebP ? 'webp' : 'jpg'
                  }/q/90`">
                </c-picture>
              </div>
              <div class="photo-card" @click="goUserDetail(rightUser.id)">
                <!-- <img :src="getImageUrl(rightUser.profileImages)" alt="生活照" class="user-photo"> -->
                <c-picture class="user-photo" :src="getImageUrl(rightUser.profileImages)" :type="`?imageView2/1/w/480/h/600/format/${supportWebP ? 'webp' : 'jpg'
                  }/q/90`">
                </c-picture>
              </div>
            </div>
          </div>

          <!-- 用户信息区域 -->
          <div class="user-info-section">
            <div class="users-info-container">
              <div class="user-info-card">
                <div class="user-name">{{ leftUser.nickName }}</div>
                <div class="user-basic">{{ leftUser.age }}岁 · {{ leftUser.occupation }}</div>
                <div class="user-location">{{ leftUser.location }}</div>
                <div class="user-detail">{{ leftUser.maritalStatus }} · {{ leftUser.height }}cm</div>
                <div class="user-intro">{{ leftUser.introduction }}</div>
              </div>
              <div class="user-info-card">
                <div class="user-name">{{ rightUser.nickName }}</div>
                <div class="user-basic">{{ rightUser.age }}岁 · {{ rightUser.occupation }}</div>
                <div class="user-location">{{ rightUser.location }}</div>
                <div class="user-detail">{{ rightUser.maritalStatus }} · {{ rightUser.height }}cm</div>
                <div class="user-intro">{{ rightUser.introduction }}</div>
              </div>
            </div>
          </div>

          <!-- 联系方式区域 -->
          <div v-if="!isShareMode" class="contact-section">
            <div class="contact-title">联系方式</div>
            <div class="contact-item">
              <div class="contact-icon"><van-icon name="wechat" color="#07c160" size="0.69rem" /></div>
              <div class="contact-info">
                <div class="contact-label">微信号
                </div>
                <div class="contact-value" v-if="isContactUnlocked" @click="copyContact(contactWechat)">{{ contactWechat
                }} <span v-if="isContactUnlocked" class="contact-tip">(点击微信号即可复制)</span></div>
                <div class="contact-tip" v-else>月老审核通过后将为您增加{{ djVotes }}票</div>
              </div>
              <div class="contact-status" v-if="!isContactUnlocked">暂未公开</div>
            </div>
          </div>

          <!-- 投票进度显示区域 -->
          <div class="vote-progress-section">
            <div class="progress-header">
              <div class="progress-title">集齐<span class="highlight-text">{{ targetVotes }}</span>票"天生一对"，CP双方将解锁微信号
              </div>
              <div class="progress-count">当前进度：<span class="highlight-text">{{ likeCount }}</span>/{{ targetVotes }}
              </div>
            </div>
            <div class="progress-bar-container">
              <div class="progress-bar" :style="{ width: progressPercentage + '%' }">
              </div>
            </div>
            <div class="unlock-tip" v-if="likeCount >= targetVotes">
              <i class="van-icon van-icon-checked"></i> 已解锁双方联系方式！
            </div>
            <!-- <div class="progress-stat" v-else-if="isShareMode && !isContactUnlocked">
              <div class="stat-item">
                <div class="stat-value negative">{{ unLikeCount }}</div>
                <div class="stat-label">我觉得悬</div>
              </div>
              <div class="stat-item">
                <div class="stat-value positive">{{ likeCount }}</div>
                <div class="stat-label">天生一对</div>
              </div>
            </div> -->
          </div>

          <!-- 分享提示区域 -->
          <div class="share-tip-section" v-if="!isShareMode && !isContactUnlocked">
            <div class="share-tip-content">
              <i class="van-icon van-icon-share"></i>
              <span class="share-tip-text">分享给好友，一起来助力吧！</span>
            </div>
            <button class="share-button" @click="share('show')">
              <i class="van-icon van-icon-friends"></i> 邀请好友投票
            </button>
          </div>

          <!-- 底部间距 -->
          <div class="bottom-spacer"></div>

          <!-- 底部操作区 -->
          <div class="bottom-actions">
            <!-- 正常访问模式 -->
            <div v-if="!isShareMode">
              <button class="action-button contact-button" @click="contactMatchmaker">
                <van-icon name="contact" size="20" />
                <span>{{ hasApplyDJHelp ? '已联系月老' : '联系月老' }}</span>
              </button>
              <div v-if="contactTipText" class="tip-text">{{ contactTipText }}</div>
            </div>

            <!-- 分享访问模式 -->
            <template v-else>
              <div class="vote-buttons-container">
                <template v-if="!hideVoteButton">
                  <button class="vote-button negative-vote" :class="{ 'voted': hasVoted && !voteResult }"
                    @click="voteCP(false)">
                    <i v-if="hasVoted && !voteResult" class="van-icon van-icon-success"></i>
                    <i v-else class="icon_jglh icon-icon-xinsui"></i>
                    {{ hasVoted && !voteResult ? '已投票' : '我觉得悬' }}
                  </button>
                  <button class="vote-button positive-vote" :class="{ 'voted': hasVoted && voteResult }"
                    @click="voteCP(true)">
                    <i class="van-icon" :class="[hasVoted && voteResult ? 'van-icon-success' : 'van-icon-like']"></i>
                    {{ hasVoted && voteResult ? '已投票' : '天生一对' }}
                  </button>
                </template>
                <template v-else>
                  <button class="vote-button positive-vote-copy">
                    您已投票，不可重复投票
                  </button>
                </template>
              </div>
              <div class="tip-text" v-if="!hasVoted">您的投票将帮助CP双方解锁联系方式</div>
              <div class="tip-text" v-else-if="hasVoted && voteResult">
                感谢您的支持！您是第 <span class="vote-number">{{ likeCount }}</span> 位看好者
                <span v-if="isKeyVoter" class="key-voter">恭喜您，您的投票解锁了CP联系方式！</span>
              </div>
              <div class="tip-text" v-else>感谢您的参与！您的意见很重要</div>
            </template>
          </div>
        </div>

        <!-- 月老选择弹窗 -->
        <div class="matchmaker-modal" v-if="showMatchmakerModal">
          <div class="modal-mask" @click="showMatchmakerModal = false"></div>
          <div class="modal-content">
            <div class="modal-header">
              <div class="modal-title">选择月老</div>
              <div class="modal-close" @click="showMatchmakerModal = false">
                <van-icon name="cross" />
              </div>
            </div>
            <div class="modal-body">
              <div class="matchmaker-list">
                <div v-for="matchmaker in matchmakers" :key="matchmaker.id" class="matchmaker-card"
                  :class="{ 'selected': selectedMatchmakerId === matchmaker.id }"
                  @click="selectMatchmaker(matchmaker.id)">
                  <div class="matchmaker-avatar">
                    <img :src="matchmaker.avatar" alt="月老头像">
                    <div class="selected-mark" v-if="selectedMatchmakerId === matchmaker.id">
                      <van-icon name="success" color="#fff" size="20" />
                    </div>
                  </div>
                  <div class="matchmaker-info">
                    <div class="matchmaker-name">{{ matchmaker.name }}</div>
                    <div class="matchmaker-desc">{{ matchmaker.description }}</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button class="confirm-button" :disabled="!selectedMatchmakerId" @click="confirmMatchmaker">
                确认选择
              </button>
            </div>
          </div>
        </div>
      </template>
    </content-view>
  </container>
</template>

<script>
// 工具
import { AppStatus } from '@/enums';
import { toast, loading } from '@/bus';
import { getImageURL } from '@/common/image';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { getAppURL, copyToClipboard, captureExpectionWithData } from '@/utils';
import { getCpInfo, userProcessDatingCharacterPairing, saveDatingCharacterPairingDJHelpApply } from '@/views/blind-date/api';
import { mapState } from 'vuex';

// 组件
import { Button, Icon } from 'vant';

export default {
  name: 'CouplesDetail',
  mixins: [mixinAuthRouter, mixinShare],
  components: {
    [Button.name]: Button,
    [Icon.name]: Icon,
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      // CP用户信息
      cpInfo: {},
      leftUser: {},
      rightUser: {},
      hideVoteButton: false, // 是否隐藏投票按钮
      hasVoted: false, // 用户是否已投票
      voteResult: null, // 用户的投票结果
      showShareTip: false, // 是否显示分享提示
      isKeyVoter: false, // 是否为关键投票者（解锁投票）
      voteAnimating: false, // 是否正在播放投票动画
      // 模式控制
      isShareMode: false, // 可以通过URL参数控制
      // 联系方式
      contactWechat: '',

      // 点赞相关
      likeCount: 0, // 初始看好票数
      unLikeCount: 0, // 初始不看好票数
      targetVotes: 20, // 解锁联系方式所需票数
      djVotes: 5, // 月老帮助增加的票数
      // 月老选择相关
      showMatchmakerModal: false, // 控制月老选择弹窗显示
      selectedMatchmakerId: null, // 当前选择的月老ID
      hasApplyDJHelp: false, // 用户是否已申请月老帮助
      // 月老列表数据
      matchmakers: [
        {
          id: 1,
          name: '李宪',
          avatar: require('@/views/blind-date/images/lx.jpg'),
          description: '情感解码专家，快速配对'
        },
        {
          id: 2,
          name: '张弛',
          avatar: require('@/views/blind-date/images/zc.jpg'),
          description: '情感分析师，专业撮合'
        },
        {
          id: 3,
          name: '谢晗',
          avatar: require('@/views/blind-date/images/xh.jpg'),
          description: '资深情感顾问，温柔耐心'
        }
      ]
    };
  },
  computed: {
    ...mapState(['supportWebP', 'blindDate']),
    pageTitle() {
      return '缘分详情';
    },
    // 计算进度百分比
    progressPercentage() {
      const percentage = (this.likeCount / this.targetVotes) * 100;
      return Math.min(percentage, 100); // 确保不超过100%
    },
    contactTipText() {
      return this.hasApplyDJHelp
        ? ''
        : `月老审核通过后将为您增加${this.djVotes}票"天生一对"`;
    },
    isContactUnlocked() {
      return this.likeCount >= this.targetVotes;
    },
  },
  mounted() {
    // 根据URL参数判断是否为分享模式
    const urlParams = this.$route.query;
    this.isShareMode = urlParams.share === '1';
  },
  methods: {
    init() {
      getCpInfo(this.$route.params.id)
        .then(res => {
          this.status = AppStatus.READY;
          this.cpInfo = res;
          this.leftUser = res.datingUser1;
          this.rightUser = res.datingUser2;
          this.likeCount = res.likeCount;
          this.unLikeCount = res.unLikeCount;
          this.hasVoted = res.alreadySendLikeFlag;
          this.hideVoteButton = !!res.alreadySendLikeFlag;
          this.hasApplyDJHelp = res.datingCharacterPairingDJHelpApply;
          if (this.likeCount >= this.targetVotes) {
            let partner = this.cpInfo.datingUser1.id === this.blindDate.profile.id ? this.cpInfo.datingUser2 : this.cpInfo.datingUser1;
            this.contactWechat = partner.wechatId;
          }
          this.share();
          setTimeout(() => {
            this.createHearts();
          }, 1000);
        })
        .catch(err => {
          toast().tip(err || '加载失败');
          this.status = AppStatus.ERROR;
        });
    },
    getImageUrl(url) {
      if (!url) {
        return '';
      }
      let image = url.split(',')[0];
      return image;
    },
    share(action = 'config') {
      const title = '📢 好友助力！帮这对CP解锁缘分';
      const logo = getImageURL('Fr8IHG-bv-Ya5p65DzxJs53GOy8y');
      const desc = '你觉得他们合适吗？快来点赞支持！帮他们跨出相识的第一步吧！';
      const url = getAppURL(this.$route.path + '?share=1');
      const shareInfo = {
        title: title,
        desc: desc,
        imgUrl: logo,
        link: url,
      };
      if (action === 'show') {
        this.$_share(shareInfo);
      } else {
        this.$_share_update(shareInfo);
      }
    },
    goUserDetail(id) {
      this.$router.push({
        path: '/blindDate/user/' + id,
      });
    },
    // 投票功能
    voteCP(isPositive) {
      if (this.hasVoted || this.voteAnimating) {
        toast().tip('您已经投过票了');
        return;
      }

      // 设置动画标志
      this.voteAnimating = true;

      // 显示加载动画
      loading(true, '正在投票...');

      userProcessDatingCharacterPairing({
        datingCharacterPairingId: this.$route.params.id,
        type: isPositive ? 'like' : 'unlike',
      }).then(res => {
        console.log(res);
        // 更新投票结果
        this.hasVoted = true;
        this.voteResult = isPositive;

        // 更新票数
        if (isPositive) {
          this.likeCount++;

          // 检查是否为第20票(关键票)
          if (this.likeCount === this.targetVotes) {
            this.isKeyVoter = true;
          }
        } else {
          this.unLikeCount++;
        }

        // 隐藏加载动画
        loading(false);

        // 播放投票成功动画
        this.playVoteAnimation(isPositive);

        // 显示投票成功提示
        let message = '';
        if (isPositive) {
          message = this.isKeyVoter
            ? '恭喜您！您的投票帮助解锁了CP联系方式！'
            : `感谢您看好这对CP！您是第 ${this.likeCount} 位看好者`;
        } else {
          message = '感谢您的宝贵意见！';
        }
        toast().tip(message);
      }).catch(err => {
        toast().tip(err || '投票失败');
      }).finally(() => {
        loading(false);
        this.voteAnimating = false;
      });
    },

    contactMatchmaker() {
      if (this.hasApplyDJHelp) {
        toast().tip('您已申请过月老帮助，不可重复申请');
        return;
      }

      // 显示月老选择弹窗
      this.showMatchmakerModal = true;
      this.selectedMatchmakerId = null; // 重置选择
    },

    // 选择月老
    selectMatchmaker(matchmakerId) {
      this.selectedMatchmakerId = matchmakerId;
    },

    // 确认选择月老并提交
    confirmMatchmaker() {
      if (!this.selectedMatchmakerId) {
        toast().tip('请选择一位月老');
        return;
      }

      // 显示加载提示
      loading(true, '正在提交申请...');

      // 获取选中的月老
      const selectedMatchmaker = this.matchmakers.find(item => item.id === this.selectedMatchmakerId);

      // 调用API
      saveDatingCharacterPairingDJHelpApply({
        daingCharacterPairingId: this.$route.params.id,
        matchMaker: selectedMatchmaker.name
      }).then(res => {
        toast().tip('已选择月老，请等待审核');
        this.init();
      }).catch(err => {
        toast().tip(err || '申请失败');
      }).finally(() => {
        loading(false);
        this.showMatchmakerModal = false;
      });
    },

    copyContact(value) {
      copyToClipboard(value)
        .then(res => {
          toast().tip('复制成功');
        })
        .catch(err => {
          captureExpectionWithData('复制相亲联系方式', {
            err,
          });
        });
    },
    // 分享给好友
    shareToFriends() {
      // 这里可以接入实际的分享功能
      toast().tip('邀请好友功能即将上线');
    },
    // 播放投票动画
    playVoteAnimation(isPositive) {
      // 创建飘心效果
      const buttonContainer = document.querySelector('.vote-buttons-container');
      const button = isPositive
        ? document.querySelector('.positive-vote')
        : document.querySelector('.negative-vote');

      if (!button) return;

      const icon = document.createElement('div');
      icon.classList.add('vote-animation');
      icon.innerHTML = isPositive
        ? '<i class="van-icon van-icon-like" style="color: #ff6b9d;"></i>'
        : '<i class="icon_jglh icon-icon-xinsui" style="color: #ff4b4b;"></i>';

      buttonContainer.appendChild(icon);

      // 设置初始位置（按钮位置）
      const buttonRect = button.getBoundingClientRect();
      icon.style.left = buttonRect.left + buttonRect.width / 2 + 'px';
      icon.style.top = buttonRect.height / 2 + 'px';

      // 添加动画类
      setTimeout(() => {
        icon.classList.add('animate');

        // 动画结束后移除元素
        setTimeout(() => {
          // document.body.removeChild(icon);
          this.voteAnimating = false;
        }, 1000);
      }, 10);
    },
    // 创建浮动爱心
    createHearts() {
      // 判断是否已经添加过
      const heart = document.querySelector('.heart');
      if (heart) {
        return;
      }
      const heartsContainer = document.getElementById('heartsContainer');
      const heartCount = 15;

      for (let i = 0; i < heartCount; i++) {
        const heart = document.createElement('div');
        heart.classList.add('heart');
        heart.innerHTML = '<i class="van-icon van-icon-like"></i>';

        // 随机位置
        heart.style.left = Math.random() * 100 + '%';
        heart.style.fontSize = (Math.random() * 20 + 10) + 'px';
        heart.style.animationDuration = (Math.random() * 6 + 4) + 's';
        heart.style.animationDelay = Math.random() * 5 + 's';

        heartsContainer.appendChild(heart);
      }
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    onResume() {
      this.init();
    },
    onReady() {
      this.init();
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/variable/global.scss';

.header-right {
  display: flex;
  align-items: center;

  .share-btn {
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon_jglh {
      font-size: 24px;
    }
  }
}

.cp-detail-container {
  margin: 0 auto;
  background: #fff;
  min-height: 100%;
  position: relative;

  /* 顶部背景区域 */
  .top-background {
    height: 200px;
    // background: linear-gradient(135deg, #ffb3d1 0%, #ffc9e0 100%);
    background: url(./images/couple.jpg) no-repeat center center;
    background-size: cover;
    position: relative;
    overflow: hidden;

    .header-content {
      position: relative;
      z-index: 2;
      text-align: center;
      padding-top: 24px;

      h1 {
        font-size: 28px;
        margin-bottom: 2px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        letter-spacing: 1px;
        color: #FFB6C7;
      }

      p {
        font-size: 18px;
        opacity: 0.9;
        margin: 0 auto;
        font-weight: 300;
        color: #FFB6C7;
      }
    }
  }

  /* CP照片区域 */
  .cp-photos-section {
    padding: 0 20px;
    margin-top: -80px;
    position: relative;
    z-index: 10;
  }

  .photos-container {
    display: flex;
    gap: 16px;
    margin-bottom: 10px;
  }

  .photo-card {
    flex: 1;
    height: 200px;
    // background: #fff;
    border-radius: 12px;
    overflow: hidden;
    // box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .user-photo {
    display: block;
    width: 100%;
    height: 200px;
    object-fit: cover;
  }

  /* 用户信息区域 */
  .user-info-section {
    padding: 0 20px 20px;
  }

  .users-info-container {
    display: flex;
    gap: 16px;
  }

  .user-info-card {
    flex: 1;
    background: #fff;
  }

  .user-name {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
  }

  .user-basic {
    font-size: 12px;
    color: #666;
    margin-bottom: 2px;
  }

  .user-location {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
  }

  .user-detail {
    font-size: 12px;
    color: #999;
    margin-bottom: 8px;
  }

  .user-intro {
    font-size: 12px;
    color: #333;
    line-height: 1.5;
    background: #f8f8f8;
    padding: 12px;
    border-radius: 8px;
    margin-top: 8px;
  }

  /* 投票进度显示区域 */
  .vote-progress-section {
    margin: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #fff5f5 0%, #ffebee 100%);
    border-radius: 12px;
    text-align: center;
    border: 1px solid rgba(255, 107, 157, 0.2);
  }

  .progress-header {
    margin-bottom: 16px;
  }

  .progress-title {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
  }

  .highlight-text {
    color: #ff6b9d;
    font-weight: bold;
  }

  .progress-count {
    font-size: 12px;
    color: #999;
  }

  .progress-bar-container {
    background: #f0f0f0;
    height: 12px;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 8px;
    position: relative;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #ff6b9d 0%, #ff8fab 100%);
    transition: width 0.8s cubic-bezier(0.22, 0.61, 0.36, 1);
    position: relative;
    border-radius: 6px;
  }

  .unlock-tip {
    font-size: 14px;
    color: #ff6b9d;
    margin-top: 12px;
    font-weight: bold;
    animation: pulse 2s infinite;
  }

  .progress-stat {
    display: flex;
    justify-content: space-around;
    margin-top: 12px;
  }

  .stat-item {
    text-align: center;
    flex: 1;
  }

  .stat-value {
    font-size: 18px;
    font-weight: bold;
  }

  .stat-value.positive {
    color: #ff6b9d;
  }

  .stat-value.negative {
    color: #ff4b4b;
  }

  .stat-label {
    font-size: 12px;
    color: #999;
    margin-top: 4px;
  }

  .share-tip-section {
    margin: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #fff5f5 0%, #ffebee 100%);
    border-radius: 12px;
    text-align: center;
    border: 1px solid rgba(255, 107, 157, 0.2);
    animation: fadeIn 0.5s ease;
  }

  .share-tip-content {
    margin-bottom: 16px;
  }

  .share-tip-text {
    color: #ff6b9d;
    font-size: 14px;
    font-weight: 500;
    margin-left: 6px;
  }

  .share-button {
    background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%);
    color: white;
    border: none;
    border-radius: 24px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(255, 107, 157, 0.3);
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .share-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4);
  }

  .share-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(255, 107, 157, 0.2);
  }

  .share-button i {
    margin-right: 6px;
    font-size: 16px;
  }

  /* 底部操作区 */
  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    background: #fff;
    padding: 16px 20px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    z-index: 100;
  }

  .action-button {
    width: 100%;
    height: 48px;
    border: none;
    border-radius: 24px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .contact-button {
    background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%);
    color: white;
  }

  .contact-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
  }

  .tip-text {
    text-align: center;
    font-size: 12px;
    color: #999;
    margin-top: 4px;
  }

  .vote-buttons-container {
    display: flex;
    gap: 12px;
    margin-bottom: 8px;
    position: relative;
  }

  .vote-button {
    flex: 1;
    height: 48px;
    border: none;
    border-radius: 24px;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    color: white;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .vote-button:active {
    transform: scale(0.95);
  }

  .vote-button:disabled {
    cursor: default;
    transform: none;
  }

  .vote-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0);
    transition: background 0.3s ease;
  }

  .vote-button:active::before {
    background: rgba(255, 255, 255, 0.2);
  }

  .icon-icon-xinsui {
    font-size: 18px;
    line-height: 1;
  }

  .vote-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 2px;
  }

  .vote-desc {
    font-size: 12px;
    opacity: 0.8;
  }

  .negative-vote {
    background: linear-gradient(135deg, #ff6a6a 0%, #ff4b4b 100%);

    &.voted {
      background: #ff4b4b;
      box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.1);
      transform: translateY(0);
      opacity: 0.8;
    }
  }

  .negative-vote:hover:not(.voted):not(:disabled) {
    box-shadow: 0 4px 15px rgba(255, 75, 75, 0.3);
    transform: translateY(-2px);
  }

  .positive-vote,
  .positive-vote-copy {
    background: linear-gradient(135deg, #F3A1BA 0%, #EB5583 100%);

    &.voted {
      background: #EB5583;
      box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.1);
      transform: translateY(0);
      opacity: 0.8;
    }
  }

  .positive-vote:hover:not(.voted):not(:disabled) {
    box-shadow: 0 4px 15px rgba(235, 85, 131, 0.3);
    transform: translateY(-2px);
  }

  .tip-text {
    text-align: center;
    font-size: 12px;
    color: #999;
    margin-top: 8px;
    line-height: 1.5;
  }

  .vote-number {
    color: #ff6b9d;
    font-weight: bold;
  }

  .key-voter {
    display: block;
    color: #ff6b9d;
    font-weight: bold;
    margin-top: 4px;
    animation: pulse 2s infinite;
  }

  /* 底部间距 */
  .bottom-spacer {
    height: 120px;
  }

  /* 隐藏类 */
  .hidden {
    display: none;
  }

  ::v-deep .heart {
    position: absolute;
    color: #FFB1CB;
    animation: float 8s linear infinite backwards;
    font-size: 1.5rem;
  }

  @keyframes float {
    0% {
      transform: translateY(100vh) rotate(0deg);
      opacity: 0;
    }

    10% {
      opacity: 1;
    }

    90% {
      opacity: 0.8;
    }

    100% {
      transform: translateY(-100px) rotate(360deg);
      opacity: 0;
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
    }

    50% {
      transform: scale(1.05);
    }

    100% {
      transform: scale(1);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

}

/* 投票动画 - 全局样式，不受scoped限制 */
::v-deep .vote-animation {
  position: fixed;
  z-index: 9999;
  font-size: 24px;
  transform: translate(-50%, -50%);
  transition: all 0.8s cubic-bezier(0.22, 0.61, 0.36, 1);
  opacity: 1;
  pointer-events: none;
}

::v-deep .vote-animation.animate {
  transform: translate(-50%, calc(-50% - 100px)) scale(1.5);
  opacity: 0;
}

::v-deep .vote-animation i {
  filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));
}

/* 联系方式区域 */
.contact-section {
  margin: 0 20px 20px;
  background: #fff;
}

.contact-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8f8f8;
  border-radius: 8px;
  margin-bottom: 12px;
}

.contact-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  color: #07c160;
}

.contact-info {
  flex: 1;
}

.contact-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.contact-value {
  font-size: 18px;
  color: #333;
  margin-top: 2px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .contact-tip {
    margin-left: 5px;
  }
}

.contact-tip {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.contact-status {
  font-size: 12px;
  color: #999;
  padding: 4px 8px;
  background: #f0f0f0;
  border-radius: 12px;
}

/* 月老选择弹窗样式 */
.matchmaker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  position: relative;
  width: 90%;
  max-width: 370px;
  max-height: 80vh;
  background: #fff;
  border-radius: 16px;
  z-index: 2;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f5f5f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-right: -12px;
  color: #999;
}

.modal-body {
  padding: 16px 20px;
  flex: 1;
  overflow-y: auto;
  max-height: 60vh;
}

.matchmaker-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.matchmaker-card {
  display: flex;
  padding: 16px;
  border-radius: 12px;
  background: #f8f8f8;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  position: relative;
}

.matchmaker-card.selected {
  border-color: #ff6b9d;
  background: #fff5f7;
}

.matchmaker-avatar {
  width: 60px;
  height: 60px;
  margin-right: 16px;
  position: relative;
}

.matchmaker-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  display: block;
}

.selected-mark {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 24px;
  height: 24px;
  background: #ff6b9d;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(255, 107, 157, 0.4);
  transform: translate(0, 0);
  animation: bounceIn 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
}

.matchmaker-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.matchmaker-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
}

.matchmaker-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid #f5f5f5;
  display: flex;
  justify-content: center;
}

.confirm-button {
  width: 100%;
  height: 44px;
  border-radius: 22px;
  border: none;
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%);
  color: white;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(255, 107, 157, 0.3);
}

.confirm-button:disabled {
  background: #cccccc;
  box-shadow: none;
  cursor: not-allowed;
  opacity: 0.7;
}

.confirm-button:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(255, 107, 157, 0.4);
}

.confirm-button:not(:disabled):active {
  transform: translateY(0);
  box-shadow: 0 2px 5px rgba(255, 107, 157, 0.3);
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes bounceIn {
  from {
    transform: scale(0) translate(10px, 10px);
  }

  to {
    transform: scale(1) translate(0, 0);
  }
}
</style>
