import { handleError } from '../error-handler';

export default [
  {
    path: '/blind-date/home',
    name: 'BlindDateHome',
    component: resolve => {
      import(
        /* webpackChunkName: "blind-date" */ '@/views/blind-date/index.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
    meta: {
      keepAlive: true,
      title: '缘分天空',
    },
  },
  {
    path: '/blind-date/rank',
    name: 'BlindDateRank',
    component: resolve => {
      import(/* webpackChunkName: "blind-date" */ '@/views/blind-date/rank.vue')
        .then(resolve)
        .catch(handleError);
    },
    meta: {
      title: '相亲榜单',
    },
  },
  {
    path: '/blind-date/apply',
    name: 'BlindDateApply',
    component: resolve => {
      import(
        /* webpackChunkName: "blind-date" */ '@/views/blind-date/apply.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
    meta: {
      title: '相亲报名',
    },
  },
  {
    path: '/blind-date/activities',
    name: 'BlindDateActivities',
    component: resolve => {
      import(
        /* webpackChunkName: "blind-date" */ '@/views/blind-date/activities.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
    meta: {
      title: '相亲活动',
    },
  },
  {
    path: '/blind-date/profile',
    name: 'BlindDateProfile',
    component: resolve => {
      import(
        /* webpackChunkName: "blind-date" */ '@/views/blind-date/profile.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
    meta: {
      title: '我的资料',
      requireAuth: true,
    },
  },
  {
    path: '/blind-date/user/:id',
    name: 'BlindDateUserDetail',
    component: resolve => {
      import(
        /* webpackChunkName: "blind-date" */ '@/views/blind-date/user-detail.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
    meta: {
      title: '用户详情',
    },
  },
];
