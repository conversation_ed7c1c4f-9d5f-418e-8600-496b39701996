<template>
  <content-view :status="status" @reload="reload" @scroll-bottom="onScrollBottom">
    <div class="activity-view">
      <div class="header-tabs">
        <div class="tab-item" :class="{ active: activeTab === 1 }" @click="switchTab(1)">
          进行中
        </div>
        <div class="tab-item" :class="{ active: activeTab === 2 }" @click="switchTab(2)">
          已结束
        </div>
      </div>

      <div class="activity-list">
        <div v-if="loading" class="loading">加载中...</div>
        <div v-else-if="activities.length === 0" class="empty-container">
          <div class="empty-icon">
            <i class="iconfont icon-empty"></i>
          </div>
          <div class="empty-text">{{ emptyText }}</div>
          <div class="empty-subtext">稍后再来看看吧</div>
          <div class="empty-action">
            <div class="refresh-btn" @click="reload">刷新</div>
          </div>
        </div>
        <div v-else>
          <!-- 活动卡片列表 -->
          <div class="activity-card" v-for="(item, index) in activities" :key="index">
            <div class="activity-image" @click="register(item)">
              <img :src="getImageUrl(item.image)" alt="活动图片" />
            </div>
            <div class="activity-info">
              <div class="activity-title">{{ item.actionTitle }}</div>
              <div class="activity-status" :class="item.status === 1
                ? 'ongoing'
                : item.status === 2
                  ? 'ended'
                  : 'coming'
                ">
                {{
                  item.status === 1
                    ? '进行中'
                    : item.status === 2
                      ? '已结束'
                      : '即将开始'
                }}
              </div>
            </div>
            <div class="activity-details">
              <div class="detail-item">
                <i class="icon_jglh icon-rili"></i>
                {{ item.activityDateTime }}
                <!-- {{ formatDateRange(item) }} -->
              </div>
              <div class="detail-item">
                <i class="icon_jglh icon-weizhi"></i>
                {{ item.location }}
              </div>
              <!-- <div class="detail-item">
                <i class="icon_jglh icon-yibaoming"></i>
                已报名 {{ item.registered }}/{{ item.capacity }} 人
              </div> -->
            </div>
            <div class="activity-footer">
              <div v-if="item.isPaid" class="price">¥{{ item.feeAmount }}/人</div>
              <div v-else class="price">免费</div>
              <div class="register-btn" :class="{ 'btn-disabled': disabledRegister(item.status) }"
                @click="register(item)">
                {{ getStatusText(item.status) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </content-view>
</template>

<script>
// 工具
import { AppStatus } from '@/enums';
import { toast, loading } from '@/bus';
import { getImageURL } from '@/common/image';
import { formatDate } from '@/utils';
import { getDatingActivitiesList } from '@/views/blind-date/api';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { mapState } from 'vuex';
// 组件
import { Button } from 'vant';
import ContentView from '@/components/ContentView.vue';

export default {
  name: 'BlindDateActivities',
  mixins: [mixinAuthRouter, mixinShare],
  components: {
    [Button.name]: Button,
    ContentView
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      loading: true,
      activeTab: 1, // 默认显示进行中的活动
      activities: [],
      activityStatus: 1, // 活动状态 1:进行中 2:已结束 0:等待中 -1:已删除
      paging: {
        page: 1,
        pageSize: 300,
      },
    };
  },
  computed: {
    ...mapState({
      profile: state => state.blindDate.profile,
    }),
    pageTitle() {
      return '相亲活动';
    },
    // 根据当前选中的标签页返回不同的空状态文案
    emptyText() {
      return this.activeTab === 1 ? '暂无进行中的活动' : '暂无已结束的活动';
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.loading = true;
      getDatingActivitiesList({
        status: this.activeTab,
        ...this.paging,
      })
        .then(res => {
          this.activities = res.list;
          this.status = AppStatus.READY;
        })
        .catch(err => {
          toast().tip(err.message || '加载失败');
          this.status = AppStatus.ERROR;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    switchTab(tab) {
      this.activeTab = tab;
      this.paging.page = 1;
      this.init();
    },
    register(item) {
      if (this.disabledRegister(item.status)) {
        return;
      }
      // 跳转到报名详情页
      this.$_auth_requireLoggedIn().then(() => {
        // 检查用户是否已填写资料
        if (this.profile.nickName) {
          // 已填写资料，直接跳转至活动
          const path = `/actions/plugins/template/signup/?id=${item.actionId}&actionId=${item.actionId}`;
          this.toActionsPage(path);
        } else {
          // 未填写资料，提示
          toast().tip('请先完善个人资料');
        }
      });
    },
    toActionsPage(url) {
      this.$_router_pageTo(`${location.origin}${url}`, {
        titleBar: true,
        shareButton: true,
      });
    },
    disabledRegister(status) {
      return status !== 1;
    },
    getStatusText(status) {
      if (status != null) {
        status = status.toString();
      }
      const statusMap = {
        0: '即将开始',
        1: '立即报名',
        2: '活动结束',
        '-1': '活动结束',
      };
      return statusMap[status] || '活动结束';
    },
    formatDateRange(action) {
      return `${formatDate(action.startTime, 'MM月DD日 HH:mm')} - ${formatDate(
        action.endTime,
        'MM月DD日 HH:mm'
      )}`;
    },
    getImageUrl(url) {
      if (!url) {
        return '';
      }
      let image = url.split(',')[0];
      return image.startsWith('http') ? image : getImageURL(image);
    },
    onLeave() {
      console.log('leave');
      this.status = AppStatus.LOADING;
    },
    onResume() {
      this.init();
    },
    onReady() {
      this.init();
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    onScrollBottom() {
      // this.$emit('scroll-bottom');
    },
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/variable/global.scss';

.activity-view {
  padding-bottom: 66px;
}

.header-tabs {
  display: flex;
  justify-content: center;
  padding: 15px 0;
  background-color: #fff;

  .tab-item {
    position: relative;
    font-size: 16px;
    color: #999;
    padding: 0 20px;

    &.active {
      color: #ff4e78;
      font-weight: 500;

      &::after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background-color: #ff4e78;
        border-radius: 1px;
      }
    }
  }
}

.activity-list {
  padding: 10px 15px;
}

.loading {
  text-align: center;
  padding: 10px 0;
}

// 空状态样式
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .empty-icon {
    font-size: 60px;
    color: #e0e0e0;
    margin-bottom: 20px;

    i {
      font-size: inherit;
    }
  }

  .empty-text {
    font-size: 16px;
    font-weight: 500;
    color: #666;
    margin-bottom: 8px;
  }

  .empty-subtext {
    font-size: 14px;
    color: #999;
    margin-bottom: 20px;
  }

  .empty-action {
    .refresh-btn {
      padding: 8px 24px;
      background-color: #ff4e78;
      color: #fff;
      font-size: 14px;
      border-radius: 20px;
      box-shadow: 0 2px 8px rgba(255, 78, 120, 0.2);
    }
  }
}

.activity-card {
  margin-bottom: 15px;
  border-radius: 8px;
  background-color: #fff;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  &:last-child {
    margin-bottom: 0;
  }

  .activity-image {
    width: 100%;
    height: 180px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .activity-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px 5px;

    .activity-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }

    .activity-status {
      font-size: 12px;
      color: #4caf50;
      padding: 4px 8px;
      border-radius: 9999px;

      &.ongoing {
        background: #f0fdf4;
      }

      &.ended {
        color: #999;
        background: #f3f4f6;
      }

      &.coming {
        background-color: #1890ff;
      }

      &.finished {
        background-color: #f3f4f6;
      }
    }
  }

  .activity-details {
    padding: 0 15px 12px;

    .detail-item {
      display: flex;
      align-items: center;
      font-size: 13px;
      color: #666;
      margin-bottom: 5px;

      i {
        margin-right: 5px;
        font-size: 14px;
        color: #999;
      }
    }
  }

  .activity-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-top: 1px solid #f5f5f5;

    .price {
      font-size: 16px;
      font-weight: 500;
      color: #ff4e78;
    }

    .register-btn {
      padding: 8px 20px;
      background-color: #ff4e78;
      color: #fff;
      font-size: 14px;
      border-radius: 20px;

      &.btn-disabled {
        background-color: #ddd;
      }
    }
  }
}
</style>
