<template>
  <container
    class="keyword-activity"
    @ready="init"
    @leave="onLeave"
    @resume="onResume"
    :keep-alive="keepAlive"
  >
    <x-header :title="pageTitle">
      <x-button slot="left" type="back"></x-button>
      <div slot="right" class="share">
        <i class="icon_jglh icon-fenxiang1" @click="share('show')"></i>
      </div>
    </x-header>
    <content-view ref="content" :status="status" @reload="reload">
      <div class="banner"></div>
      <div class="content">
        <div class="card">
          <p>去 ________ 商城, 囤年货</p>
          <van-field
            v-model="keyword"
            label=""
            input-align="center"
            placeholder="请输入暗号"
          />
          <span v-if="showError" class="error-tip">(提示：{{ errorTip }})</span>
        </div>
        <van-button
          class="btn"
          type="primary"
          round
          :disabled="disabled"
          :loading="loading"
          loading-text="领取中"
          @click="doReceive"
          >立即领取</van-button
        >
        <van-button
          class="btn btn-white"
          type="default"
          round
          @click="toTickets"
          >查看我的奖励</van-button
        >
      </div>
      <PrizePop
        v-if="showPrize"
        :show="showPrize"
        :prize="prizeInfo"
        @cancel="handleTipsClose"
        @confirm="handleTipsConfirm"
      >
      </PrizePop>
    </content-view>
  </container>
</template>

<script>
import { isInJglh, isInWeixin } from '@/common/env';
import { getVersion, parseJglhURL } from '@/bridge';
import { AppStatus, PrizeType as PrizeTypeEnum } from '@/enums';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { getImageURL } from '@/common/image';
import { formatDate, getAppURL } from '@/utils';
import { dialog, toast, loading } from '@/bus';
import { receiveAward, getKeywordCouponAction } from './api';
import BizRichText from '@/views/components/BizRichText.vue';
import PrizePop from './components/PrizePop';
import { Field, Button } from 'vant';

const jglhVersion = getVersion();
export default {
  name: 'KeywordActivity',
  mixins: [mixinAuthRouter, mixinShare],
  data() {
    const ref = this.$route.query.ref || ''; // 分享uid
    return {
      AppStatus,
      status: AppStatus.LOADING,
      keepAlive: false,
      keyword: '',
      ref,
      pageData: {},
      originalPrizeInfo: {},
      shareInfo: {
        title: '对暗号赢大额优惠券',
        desc: '让我们一起踏上一场味蕾之旅，探索那些令人垂涎欲滴的美食吧！',
        shareImage: 'FmFHX5pWoYS17NkmG4LjG72nO-oP',
      },
      prizeInfo: {
        prizeType: PrizeTypeEnum.COUPON, // 奖品类型 1: 优惠券 2: 实物 3: 无奖品
        lotteryAmount: 30, // 中奖金额
        prizeName: '', // 奖品名称
      },
      disabled: false,
      loading: false,
      showPrize: false,
      showError: false,
      errorTip: '',
    };
  },
  components: {
    BizRichText,
    PrizePop,
    [Field.name]: Field,
    [Button.name]: Button,
  },
  computed: {
    pageTitle() {
      return this.pageData.name || '对暗号';
    },
  },
  mounted() {},
  methods: {
    toTickets() {
      this.$_router_push('/ticket/list');
    },
    init() {
      // 初始化数据
      const id = this.$route.query.id;
      getKeywordCouponAction(id)
        .then(res => {
          this.pageData = res;
          this.disabled = res.status !== 1;
          this.status = AppStatus.READY;
          this.share();
        })
        .catch(err => {
          this.status = AppStatus.ERROR;
          toast().tip(err.message);
        });
      // this.status = AppStatus.ERROR;
    },

    doReceive: function () {
      if (!this.keyword) {
        toast().tip('请输入暗号');
        return;
      }
      this.loading = true;
      const id = this.$route.query.id;
      receiveAward({
        id,
        code: this.keyword.replace(/\s/g, ''),
      })
        .then(res => {
          this.originalPrizeInfo = res;
          // 返回多张优惠券时，取优惠券总金额
          let lotteryAmount = (res.grantCouponSettings || []).reduce(
            (acc, cur) => {
              return acc + cur.couponAmount;
            },
            0
          );
          // 返回多张优惠券时，不显示优惠券名称，返回一张优惠券时，显示优惠券名称
          let prizeName =
            res.grantCouponSettings && res.grantCouponSettings.length > 1
              ? ''
              : res.grantCouponSettings[0].couponName;
          this.prizeInfo = {
            ...this.prizeInfo,
            lotteryAmount,
            prizeName,
          };
          // if (res.prizeType == '1') {
          //   toast().tip('什么也没抽到，再接再厉哦~');
          // } else {
          // }
          this.showPrize = true;
          this.loading = false;
          this.showError = false;
        })
        .catch(err => {
          this.showError = true;
          this.errorTip =
            err.message == '口令不匹配' ? '商城名称' : err.message;
          this.loading = false;
          toast().tip(err.message);
        });
    },
    handleTipsClose() {
      this.showPrize = false;
    },
    handleTipsConfirm() {
      this.showPrize = false;
      if (this.originalPrizeInfo.jumpUrl) {
        this.$_router_pageTo(this.originalPrizeInfo.jumpUrl, {
          theme: 'light',
        });
      }
    },
    share(action = 'config') {
      const title = this.pageData.name;
      const logo = getImageURL('FiLbIHo0MPSL_IBCUJ4XwWnu7LBb');
      const desc = this.pageData.name;
      const url = getAppURL(this.$route.fullPath);
      const shareInfo = {
        title: title,
        desc: desc,
        imgUrl: logo,
        link: url,
      };
      if (action === 'show') {
        this.$_share(shareInfo);
      } else {
        this.$_share_update(shareInfo);
      }
    },
    reload() {
      this.status = AppStatus.LOADING;
      // 重新加载页面
      this.init();
    },
    onResume() {
      // 页面重新激活时在重新获取数据
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
  },
};
</script>
<style lang="scss" scoped>
@import '~styles/mixin/animate.scss';
@import '~styles/mixin/index.scss';
img {
  -webkit-user-drag: none;
}
.keyword-activity {
  background: #ff2c54;
}
.banner {
  width: 100%;
  background: url(./assets/images/keyword_banner.png) no-repeat center top;
  background-size: 100% auto;
  padding-top: 64%;
  margin-bottom: 42px;
  // overflow: hidden;
}
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  .card {
    background: #ffe9dc;
    border-radius: 15px 15px 15px 15px;
    width: 317px;
    box-sizing: border-box;
    padding: 48px 15px 30px;
    position: relative;
    text-align: center;
    margin-bottom: 20px;
    &::before {
      content: '';
      position: absolute;
      top: -25px;
      left: 50%;
      z-index: 1;
      transform: translateX(-50%);
      width: 173px;
      height: 50px;
      background: url(./assets/images/keyword_title.png) no-repeat center;
      background-size: 100% auto;
    }
    p {
      font-size: 17px;
      color: #72100c;
      line-height: 20px;
      text-align: center;
      margin-bottom: 18px;
    }
    .van-field {
      width: 240px;
      height: 40px;
      line-height: 40px;
      padding-top: 0;
      padding-bottom: 0;
      border-radius: 20px;
      margin: 0 auto;
      font-size: 16px;
    }
    .error-tip {
      display: block;
      color: $lh-2022-primary-color;
      font-size: 12px;
      padding-top: 10px;
    }
  }
  .btn {
    width: 288px;
    height: 48px;
    margin-bottom: 20px;
    background: linear-gradient(90deg, #ffffff 0%, #ffda69 100%);
    border: none;
    color: #72100c;
    font-size: 17px;
  }
  .btn-white {
    background: #fff;
  }
}
.share {
  width: 45px;
  height: 45px;
  text-align: center;
  line-height: 46px;
  .icon_jglh {
    font-size: 24px;
  }
}
</style>
