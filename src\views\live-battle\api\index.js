import APIModel from '@/api/APIModel';
import { doGet, doPost, postJSON } from '@/api/request/';

/**
 * 接口文档：http://jgrm.net:10230/swagger-ui.html#/%E7%94%B5%E5%8F%B0%E6%A0%8F%E7%9B%AE-%E8%8A%82%E7%9B%AE%E7%AE%A1%E7%90%86/detailUsingGET_1
 */
const api = new APIModel({
  '/pk/action/detail': '/Radio/action/question/action/config/detail', // 活动详情
  '/pk/rooms/create': '/Radio/pk/rooms/create', // 创建房间
  '/pk/rooms/join': '/Radio/pk/rooms/{roomId}/join', // 加入房间
  '/pk/rooms/list': '/Radio/pk/wait/rooms/list', // 获取等待中的房间列表
});

/**
 * @description: 创建房间
 * @param {string} actionId 活动id
 * @param {string} uid 用户id
 * @return {*}
 */
export function createRoom(params) {
  return doPost(api.render('/pk/rooms/create'), params);
}
/**
 * @description: 加入房间
 * @param {string} roomId
 * @param {object} uid 用户id
 * @return {*}
 */
export function joinRoom(roomId, params) {
  return doPost(api.render('/pk/rooms/join', { roomId }), params);
}

/**
 * @description: 获取等待中的房间列表
 * @param {string} actionId 活动id
 * @return {*}
 */
export function getRoomsList(params) {
  return api.doGet(api.render('/pk/rooms/list'), params);
}

export function getActionDetail(actionId) {
  return api.doGet(api.render('/pk/action/detail'), { actionId });
}
