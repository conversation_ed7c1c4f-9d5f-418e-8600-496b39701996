import SockJS from 'sockjs-client';
import { Client } from '@stomp/stompjs';

class QuizService {
  constructor() {
    this.socket = null;
    this.stompClient = null;
    this.connected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 10;
    this.reconnectDelay = 2000;
    this.subscriptions = [];
    this.connectionPromise = null;
    this.serverUrl = null;
    this.userId = null;
    this.roomId = null;
    this.userDisconnect = false;
    this.connectionCallbacks = {
      onConnect: () => {},
      onDisconnect: () => {},
      onError: () => {},
    };
  }

  connect(serverUrl, headers, callbacks = {}) {
    if (typeof serverUrl !== 'string' || !serverUrl.trim()) {
      return Promise.reject(new Error('Invalid server URL'));
    }

    if (this.connectionPromise) {
      return this.connectionPromise;
    }
    const { roomId, userId } = headers;
    this.serverUrl = serverUrl;
    this.roomId = roomId;
    this.userId = userId;
    this.connectionCallbacks = { ...this.connectionCallbacks, ...callbacks };

    this.connectionPromise = new Promise((resolve, reject) => {
      try {
        const socketUrl = this.serverUrl;
        this.socket = new SockJS(socketUrl);

        this.userDisconnect = false;
        this.stompClient = new Client({
          // 连接时携带的头信息
          connectHeaders: {
            roomId,
            userId,
          },
          webSocketFactory: () => this.socket,
          debug:
            process.env.NODE_ENV === 'development' ? console.debug : () => {},
          reconnectDelay: 0, // 禁用库自带的自动重连（使用自定义逻辑）
          heartbeatIncoming: 10000,
          heartbeatOutgoing: 10000,
        });

        this.stompClient.onConnect = frame => {
          this.connected = true;
          // this.reconnectAttempts = 0; // 会导致不停重连
          this.connectionCallbacks.onConnect(frame);
          resolve(frame);
        };

        this.stompClient.onStompError = error => {
          this.connected = false;
          this.connectionCallbacks.onError(error);
          // TODO 区分错误类型（如认证失败），部分错误可能无需重连。
          if (error.headers?.message === 'Authentication failed') {
            reject(new Error('Authentication failed'));
          } else {
            // this.attemptReconnect();
          }
        };

        this.stompClient.onWebSocketClose = e => {
          this.connected = false;
          this.connectionCallbacks.onDisconnect();
          // !this.userDisconnect && this.attemptReconnect();
        };

        this.stompClient.activate();
      } catch (error) {
        this.connectionPromise = null;
        reject(error);
      }
    });

    return this.connectionPromise;
  }

  subscribe(destination, callback) {
    if (!this.connected || !this.stompClient) {
      console.error('Subscribe failed: Not connected');
      return null;
    }

    try {
      const subscription = this.stompClient.subscribe(destination, message => {
        try {
          callback(JSON.parse(message.body));
        } catch (error) {
          console.error('Message parse error:', error);
          callback(message.body);
        }
      });
      this.subscriptions = [];

      this.subscriptions.push({
        id: subscription.id,
        destination,
        callback,
      });

      return subscription;
    } catch (error) {
      console.error('Subscription error:', error);
      this.connectionCallbacks.onError(error);
      return null;
    }
  }

  send(destination, body, headers = {}) {
    if (!this.connected || !this.stompClient) {
      console.error('Send failed: Not connected');
      return false;
    }

    try {
      let _headers = { ...headers, roomId: this.roomId, userId: this.userId };
      const payload = typeof body === 'string' ? body : JSON.stringify(body);
      this.stompClient.publish({ destination, _headers, body: payload });
      return true;
    } catch (error) {
      console.error('Send error:', error);
      this.connectionCallbacks.onError(error);
      return false;
    }
  }

  attemptReconnect() {
    if (
      this.reconnectAttempts >= this.maxReconnectAttempts ||
      this.userDisconnect
    ) {
      console.error('Maximum reconnect attempts reached');
      this.connectionCallbacks.onError(new Error('Max reconnect attempts'));
      return;
    }
    this.reconnectAttempts++;
    this.connectionPromise = null;

    setTimeout(() => {
      // const prevSubscriptions = [...this.subscriptions];
      this.subscriptions = [];

      this.connect(this.serverUrl, { userId: this.userId, roomId: this.roomId })
        .then(() => {
          // prevSubscriptions.forEach(sub => {
          //   this.subscribe(sub.destination, sub.callback);
          // });
        })
        .catch(error => {
          console.error('Reconnect failed:', error);
          this.attemptReconnect(); // 继续尝试重连
        });
    }, this.reconnectDelay * this.reconnectAttempts);
  }

  disconnect() {
    if (this.stompClient && this.connected) {
      this.userDisconnect = true;
      return this.stompClient.deactivate().then(() => {
        this.connected = false;
        this.connectionPromise = null;
        this.subscriptions = [];
        this.serverUrl = null;
        this.userId = null;
        this.roomId = null;
        this.connectionCallbacks.onDisconnect();
      });
    }
  }

  unsubscribe(subscriptionId) {
    if (!this.connected || !this.stompClient) return;

    const index = this.subscriptions.findIndex(s => s.id === subscriptionId);
    if (index !== -1) {
      try {
        this.stompClient.unsubscribe(subscriptionId);
        this.subscriptions.splice(index, 1);
      } catch (error) {
        console.error('Unsubscribe error:', error);
      }
    }
  }

  resetConnect() {
    this.socket = null;
    this.stompClient = null;
    this.connected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 10;
    this.subscriptions = [];
    this.connectionPromise = null;
    this.serverUrl = null;
    this.userId = null;
    this.roomId = null;
    this.userDisconnect = false;
    this.connectionCallbacks = {
      onConnect: () => {},
      onDisconnect: () => {},
      onError: () => {},
    };
  }

  isConnected() {
    return this.connected;
  }
}

// 改进的URL拼接逻辑
export const createSockJSUrl = endpoint => {
  const baseUrl = process.env.VUE_APP_SOCKET_URL || window.location.origin;
  return `${baseUrl.replace(/\/$/, '')}/${endpoint.replace(/^\//, '')}`;
};

export default new QuizService();
