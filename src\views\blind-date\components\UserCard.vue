<template>
  <div class="user-card" @click="handleClick">
    <div class="user-avatar">
      <!-- <img :src="getImageUrl(user.profileImages)" alt="用户头像" /> -->
      <c-picture class="user-photo" :src="getImageUrl(user.profileImages)" :type="`?imageView2/1/w/480/h/600/format/${supportWebP ? 'webp' : 'jpg'
        }/q/90`">
      </c-picture>
    </div>
    <div class="user-info">
      <div class="user-name-age">
        <span class="user-name">{{ user.nickName }}</span>
        <span class="user-age">{{ user.age }}岁</span>
      </div>
      <div class="user-location">
        {{ user.location }} | {{ user.occupation }}
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'UserCard',
  props: {
    user: {
      type: Object,
      required: true,
      default: () => ({
        id: '',
        name: '',
        age: '',
        location: '',
        occupation: '',
        avatarUrl: '',
      }),
    },
  },
  computed: {
    ...mapState(['supportWebP']),
  },
  methods: {
    handleClick() {
      this.$emit('click', this.user.id);
    },
    getImageUrl(url) {
      if (!url) {
        return '';
      }
      let image = url.split(',')[0];
      return image;
    },
  },
};
</script>

<style lang="scss" scoped>
.user-card {
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  background-color: #fff;
  overflow: hidden;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.05);
}

.user-avatar {
  width: 100%;
  height: 180px;
  overflow: hidden;
  flex-shrink: 0;
  border-radius: 12px 12px 0 0;

  .user-photo {
    width: 100%;
    height: 100%;
  }
}

.user-info {
  padding: 10px;
  background-color: #fff;
}

.user-name-age {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 5px;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
}

.user-age {
  font-size: 10.5px;
  font-weight: normal;
  line-height: 14px;
  color: #ff4d6d;
}

.user-location {
  font-size: 10.5px;
  font-weight: normal;
  line-height: 14px;
  letter-spacing: normal;
  color: #6b7280;
}
</style>
