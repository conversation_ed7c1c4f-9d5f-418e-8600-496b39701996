import { mapActions, mapGetters } from 'vuex';
import { dialog } from '@/bus';
import { login } from '@/bridge';
import ENV from '@/common/env';
import { getThirdAppParams } from '@/utils';

/**
 * 适用于需要校验用户登录权限的页面
 * 校验登录状态
 */
export const mixinAuth = {
  computed: {
    ...mapGetters({
      $_auth_isLoggedIn: 'isLoggedIn',
      $_auth_isVip: 'isVip',
      $_auth_isCarVip: 'isCarVip',
      $_auth_userInfo: 'userInfo',
    }),
  },
  methods: {
    /* ...mapActions({
      '$_auth_checkSession': 'checkSession',
      // 同步全局用户资料信息
      '$_auth_syncUserInfo': 'syncUserInfo',
    }), */
    ...mapActions(['checkSession', 'syncUserInfo']),
    /**
     * 校验session是否有效
     * @returns Promise
     */
    $_auth_checkSession() {
      return this.checkSession(...arguments).then(res => {
        if (window.ga) {
          window.ga('set', 'dimension5', res.ok); // 自定义维度5：GA后台配置名称：登录状态
        }
        return res;
      });
    },
    /**
     * 请求登录权限，若未登录将跳转到登录页面
     * ```js
     * // 示例
     * this.$_auth_requireLoggedIn.then(() => {
     *   // 需要登录后才能操作的逻辑
     * })
     * // 或在methods中声明一个 async 方法
     * async function yourFunction() {
     *   await this.$_auth_requireLoggedIn();
     *   // 需要登录后才能操作的逻辑
     * }
     * ```
     * @returns Promise
     */
    $_auth_requireLoggedIn() {
      return new Promise((resolve, reject) => {
        if (!this.$_auth_isLoggedIn) {
          this.$_auth_login();
          return;
        }
        resolve();
      });
    },
    /**
     * 同步用户信息
     * 有些业务操作后需要更新本地缓存的用户信息，如购买vip，更新用户资料等
     * 调用此方法更新本地缓存的 `$_auth_userInfo` 信息
     * @returns
     */
    $_auth_syncUserInfo() {
      return this.syncUserInfo().then(res => {
        if (window.ga) {
          window.ga('set', 'userId', res.uid); // 使用已登录的 user_id 来设置用户 ID。
          window.ga('set', 'dimension4', res.uid); // 自定义维度4：GA后台配置名称：User
        }
        return res;
      });
    },
    // push到一个需要登录才能跳转的页面，若未登录将自动跳转到登录页面
    $_auth_push(data) {
      if (this.$_auth_isLoggedIn) {
        this.$router.push(...arguments);
      } else {
        return this.$_auth_login(...arguments);
      }
    },
    // replace到一个需要登录才能跳转的页面，若未登录将自动跳转到登录页面
    $_auth_replace(data) {
      if (this.$_auth_isLoggedIn) {
        this.$router.replace(data);
      } else {
        return this.$_auth_login(...arguments);
      }
    },
    // 跳转登录，若已登录不跳转 与 `$_auth_requireLoggedIn` 功能重叠
    $_auth_go(nextPage) {
      if (this.$_auth_isLoggedIn) {
        return Promise.resolve();
      }
      return this.$_auth_login(...arguments);
    },
    // 跳转登录
    $_auth_login(nextPage) {
      // debugger
      // console.log(ENV)
      if (ENV.weixin || ENV.jglh || ENV.unionpay_mp || ENV.alipay) {
        return login(nextPage).then(res => {
          return this.$_auth_checkSession(true).then(res => {
            // 若用户已登录，同步用户信息，其中包含了用户是否是vip，用户资料等信息
            if (res.ok) {
              return this.$_auth_syncUserInfo();
            }
          });
        });
      } else if (getThirdAppParams().platform) {
        login();
      } else {
        return new Promise(() => {
          dialog('提示').confirm('请在交广领航App或微信App中访问本页面', {
            ok() {
              // location.href = 'https://www.jgrm.net/mobile/jglh.html'
              // location.href = 'https://a.app.qq.com/o/simple.jsp?pkgname=com.iwxlh.pta';
              // location.href = 'https://radio.jgrm.net/actions/app/wechat/wx2app.html?id=4';
              // let path = 'https://www.jgrm.net/mobile/jglh.html'
              // 2023-02-08 运营要求，会员页面点击时跳转到打开app引导页
              // 2023-02-20 运营要求，审车页面点击时跳转到打开app引导页
              // if ((nextPage || '').indexOf('vip') > -1) {
              //   path = 'https://radio.jgrm.net/actions/app/wechat/wx2app.html?id=4'
              // }
              let path =
                'https://radio.jgrm.net/actions/app/wechat/wx2app.html?id=4';
              location.href = path;
            },
          });
        });
      }
    },
  },
};
