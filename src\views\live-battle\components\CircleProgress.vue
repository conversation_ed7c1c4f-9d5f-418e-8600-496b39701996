<template>
  <div class="circle-progress" :style="containerStyle">
    <svg :width="diameter" :height="diameter">
      <!-- 背景圆 -->
      <circle
        class="background"
        :cx="radius"
        :cy="radius"
        :r="innerRadius"
        :stroke-width="strokeWidth"
        :style="backgroundStyle"
      />

      <!-- 进度圆 -->
      <circle
        class="progress"
        :cx="radius"
        :cy="radius"
        :r="innerRadius"
        :stroke-width="strokeWidth + 1"
        :stroke-dasharray="dashArray"
        :stroke-dashoffset="dashOffset"
        :style="progressStyle"
      />
    </svg>
  </div>
</template>

<script>
export default {
  name: 'CircleProgress',
  props: {
    progress: {
      type: Number,
      default: 0,
      validator: value => value >= 0 && value <= 100,
    },
    radius: {
      type: Number,
      default: 50,
    },
    strokeWidth: {
      type: Number,
      default: 8,
    },
    strokeColor: {
      type: String,
      default: '#FD4925',
    },
    backgroundColor: {
      type: String,
      default: '#EEF2FE',
    },
  },
  computed: {
    diameter() {
      return this.radius * 2;
    },
    innerRadius() {
      return this.radius - this.strokeWidth / 2;
    },
    circumference() {
      return 2 * Math.PI * this.innerRadius;
    },
    dashArray() {
      return this.circumference;
    },
    dashOffset() {
      return this.circumference * (1 - this.progress / 100);
    },
    containerStyle() {
      return {
        width: `${this.diameter}px`,
        height: `${this.diameter}px`,
      };
    },
    progressStyle() {
      return {
        stroke: this.strokeColor,
        strokeLinecap: 'round',
      };
    },
    backgroundStyle() {
      return {
        stroke: this.backgroundColor,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.circle-progress {
  position: relative;
  display: inline-flex;
}

circle.background {
  fill: none;
  stroke: v-bind(backgroundColor);
}

circle.progress {
  fill: none;
  transform: rotate(-90deg);
  transform-origin: 50% 50%;
  transition: stroke-dashoffset 0.5s ease;
}

.progress-text {
  font-size: 20px;
  font-weight: bold;
  fill: v-bind(strokeColor);
}
</style>
