<template>
  <van-popup
    v-model="showPopup"
    overlay
    round
    :close-on-click-overlay="false"
    position="center"
    get-container="body"
    class="phone-pop"
    @close="handleClose"
  >
    <div class="pop-title">
      <h2 class="panel-title">{{ title }}</h2>
    </div>
    <div class="pop-content">
      <div class="receive-desc">
        <van-form validate-first ref="form">
          <van-field
            v-model="form.phone"
            name="phone"
            label=""
            type="tel"
            maxlength="11"
            clearable
            autocomplete="off"
            :formatter="formatterTel"
            placeholder="请输入手机号"
            :rules="[
              {
                required: true,
                pattern: patternTel,
                message: '请输入正确的手机号',
              },
            ]"
          />
        </van-form>
      </div>
    </div>
    <div class="pop-bottom-btn">
      <van-button
        style="background: #f3f3f3"
        class=""
        round
        block
        @click="handleClose"
        >取消</van-button
      >
      <van-button
        class="submit-btn"
        round
        block
        type="danger"
        loading-text="领取中"
        :loading="submitLoading"
        @click="submit"
        >确认</van-button
      >
    </div>
  </van-popup>
</template>
<script>
import { formatDate } from '@/utils';
import { mixinAuthRouter } from '@/mixins';
import { dialog, loading } from '@/bus';
import { mixinForm } from '@pkg/finance/mixins/form.js';

import {
  Checkbox,
  Button,
  Form,
  Field,
  Cell,
  CountDown,
  Toast,
  Popup,
} from 'vant';

export default {
  name: 'PhoneForm',
  props: {
    title: {
      type: String,
      default: '',
    },
    show: {
      type: Boolean,
      default: false,
    },
  },
  mixins: [mixinAuthRouter, mixinForm],
  components: {
    [Button.name]: Button,
    [Popup.name]: Popup,
    [Checkbox.name]: Checkbox,
    [Form.name]: Form,
    [Field.name]: Field,
    [Cell.name]: Cell,
    [Toast.name]: Toast,
  },
  data() {
    return {
      showPopup: this.show,
      form: {
        phone: '',
        code: '',
      },
      time: 0,
      checked: true,
      submitLoading: false,
    };
  },
  computed: {},
  watch: {
    show(val) {
      this.showPopup = val;
    },
  },
  mounted() {},
  methods: {
    ...{ formatDate },
    submit(params) {
      // if (!this.checked) {
      //   this.$toast('请勾选服务协议');
      //   return
      // }
      this.submitLoading = true;
      this.$refs.form
        .validate()
        .then(res => {
          // 登录
          this.$emit('confirm', this.form.phone);
        })
        .catch(e => {
          this.submitLoading = false;
          this.$toast(e[0].message);
        });
    },
    reset(url) {
      this.submitLoading = false;
    },
    toPage(url) {
      this.handleClose();
      this.$_auth_push(url);
    },
    handleClose() {
      this.$emit('close');
    },
  },
};
</script>
<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
.phone-pop ::v-deep {
  width: 340px;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  overflow-y: visible;
  .pop-title {
    height: 54px;
    box-sizing: border-box;
    text-align: center;
  }
  .panel-title {
    height: 100%;
    box-sizing: border-box;
    justify-content: center;
    align-items: center;
    border-bottom: none;
  }
  .pop-content {
    flex: 1;
    overflow: auto;
    padding: 15px;
    line-height: 1;
    background: #ffffff;
    &::-webkit-scrollbar {
      width: 0 !important;
    }
  }
  .receive-desc {
    font-size: 14px;
    color: #333333;
    text-align: left;
    line-height: 1.5;
  }
  .pop-bottom-btn {
    padding: 5px 15px 20px;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    .van-button {
      flex: 1;
      width: 100%;
      font-size: 15px;
      line-height: 40px;
      height: 40px;
      &:not(:first-child) {
        margin-left: 15px;
      }
    }
    .close-btn {
      background: #f3f3f3;
      border-color: #f3f3f3;
      color: #333333;
    }
    .submit-btn {
      background: linear-gradient(90deg, #ff7cac 0%, #fe3942 100%);
    }
  }
  .van-popup--center.van-popup--round {
    border-radius: 10px;
  }
  .van-icon-cross {
    top: 18px;
    font-size: 16px;
    line-height: 1;
  }
  .close-icon {
    position: absolute;
    bottom: -50px;
    left: 50%;
    transform: translateX(-50%);
    margin: 0 auto;
  }
}
.van-cell {
  border-radius: 10px;
  &:not(:last-child) {
    margin-bottom: 15px;
  }
  &.van-field {
    border: 1px solid #dddddd;
  }
  ::v-deep .van-field__error-message {
    display: none;
  }
  ::v-deep .van-field__button {
    font-size: 12px;
    padding-left: 15px;
    .van-button {
      border: none;
      background: transparent;
    }
    .van-button::before {
      display: none;
    }
  }
}
</style>
