<template>
  <div class="step-progress">
    <div class="step-item" v-for="(item, index) in steps" :key="index">
      <div class="circle-wrapper" :class="{ active: index <= activeStep }">
        <div class="circle">
          {{ index + 1 }}
        </div>
        <div v-if="index < steps.length - 1" class="line" :class="{ active: index < activeStep }"></div>
      </div>
      <div class="label">{{ item }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StepProgress',
  props: {
    activeStep: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      steps: ['基本信息', '补充资料', '择偶要求', '确认提交'],
    };
  },
};
</script>

<style lang="scss" scoped>
.step-progress {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 0px;
  background: #f8f9fa;

  .step-item {
    flex: 1;
    text-align: center;
    position: relative;

    .circle-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .circle {
        width: 28px;
        height: 28px;
        line-height: 28px;
        border-radius: 50%;
        background: #e5e5e5;
        color: #333;
        font-size: 12px;
        z-index: 2;
        transition: all 0.3s;
      }

      .line {
        position: absolute;
        left: 50%;
        top: 12px;
        height: 2px;
        width: 100%;
        background: #e5e5e5;
        z-index: 1;

        &.active {
          background: #ff4f9a;
        }
      }

      &.active {
        .circle {
          background: #ff4f9a;
          color: #fff;
        }

        .line {
          background: #ff4f9a;
        }
      }
    }

    .label {
      margin-top: 8px;
      font-size: 14px;
      color: #000;
    }
  }
}
</style>
