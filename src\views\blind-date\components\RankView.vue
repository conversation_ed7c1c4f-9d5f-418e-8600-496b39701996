<template>
  <content-view
    :status="status"
    @reload="reload"
    @scroll-bottom="onScrollBottom"
  >
    <div class="ranking-container">
      <!-- 顶部标签和日期选择 -->
      <div class="rank-type-header">
        <div class="rank-type-tabs">
          <div
            class="tab-item"
            :class="{ active: rankType === 'popularity' }"
            @click="switchRankType('popularity')"
          >
            人气榜
          </div>
          <div
            class="tab-item"
            :class="{ active: rankType === 'sendFlower' }"
            @click="switchRankType('sendFlower')"
          >
            送花榜
          </div>
        </div>
      </div>

      <!-- 周/月榜选择 -->
      <div class="ranking-sub-header">
        <div class="tabs">
          <div
            class="tab-item"
            :class="{ active: activeTab === 'weekly' }"
            @click="switchTab('weekly')"
          >
            周榜
          </div>
          <div
            class="tab-item"
            :class="{ active: activeTab === 'monthly' }"
            @click="switchTab('monthly')"
          >
            月榜
          </div>
        </div>
      </div>

      <!-- 有数据时显示排行榜 -->
      <template v-if="rankList.length > 0">
        <!-- 前三名展示区 -->
        <div class="top-three">
          <!-- 第二名 -->
          <div
            v-if="rankList[1]"
            class="rank-item rank-second"
            @click="goToUserDetail(rankList[1].id)"
          >
            <div class="rank-number">2</div>
            <div class="avatar-container">
              <img
                class="avatar"
                :src="getImageUrl(rankList[1].profileImages)"
                alt="头像"
              />
            </div>
            <div class="name">{{ rankList[1].nickName }}</div>
            <div class="score">
              <van-icon
                v-if="rankType === 'popularity'"
                name="fire"
                color="#ff5858"
                size="16"
              />
              <span v-else class="icon_jglh icon-rose"></span>
              {{ formatNumber(rankList[1].count) }}
            </div>
          </div>

          <!-- 第一名 -->
          <div
            v-if="rankList[0]"
            class="rank-item rank-first"
            @click="goToUserDetail(rankList[0].id)"
          >
            <div class="rank-number rank-first-number">1</div>
            <div class="avatar-container">
              <img
                class="avatar"
                :src="getImageUrl(rankList[0].profileImages)"
                alt="头像"
              />
            </div>
            <div class="name">{{ rankList[0].nickName }}</div>
            <div class="score">
              <van-icon
                v-if="rankType === 'popularity'"
                name="fire"
                color="#ff5858"
                size="16"
              />
              <span v-else class="icon_jglh icon-rose"></span>
              {{ formatNumber(rankList[0].count) }}
            </div>
          </div>

          <!-- 第三名 -->
          <div
            v-if="rankList[2]"
            class="rank-item rank-third"
            @click="goToUserDetail(rankList[2].id)"
          >
            <div class="rank-number">3</div>
            <div class="avatar-container">
              <img
                class="avatar"
                :src="getImageUrl(rankList[2].profileImages)"
                alt="头像"
              />
            </div>
            <div class="name">{{ rankList[2].nickName }}</div>
            <div class="score">
              <van-icon
                v-if="rankType === 'popularity'"
                name="fire"
                color="#ff5858"
                size="16"
              />
              <span v-else class="icon_jglh icon-rose"></span>
              {{ formatNumber(rankList[2].count) }}
            </div>
          </div>
        </div>

        <!-- 排名列表 -->
        <div v-if="otherRanks.length > 0" class="rank-list">
          <div
            class="rank-list-item"
            v-for="(item, index) in otherRanks"
            :key="item.id"
            @click="goToUserDetail(item.id)"
          >
            <div class="rank-item-left">
              <div class="rank-number-list">{{ index + 4 }}</div>
              <div class="avatar-small">
                <img :src="getImageUrl(item.profileImages)" alt="头像" />
              </div>
              <div class="user-info">
                <div class="user-name">{{ item.nickName }}</div>
                <div class="user-id">ID: {{ item.id }}</div>
              </div>
            </div>
            <div class="rank-item-right">
              <div class="score">
                <van-icon
                  v-if="rankType === 'popularity'"
                  name="fire"
                  color="#ff5858"
                  size="16"
                />
                <span v-else class="icon_jglh icon-rose"></span>
                {{ formatNumber(item.count) }}
              </div>
              <!-- <div class="trend-icon" :class="getTrendClass(item.trend)"></div> -->
            </div>
          </div>
        </div>
      </template>

      <!-- 空状态展示 -->
      <div v-else class="empty-state">
        <div class="empty-icon"></div>
        <div class="empty-text">暂无排名数据</div>
        <div class="empty-subtext">敬请期待下期榜单</div>
      </div>
    </div>
  </content-view>
</template>

<script>
import { getRankList } from '../api';
import { getImageURL } from '@/common/image';
import { getDateRanges } from '@/utils/datetime';
import { Icon } from 'vant';
import ContentView from '@/components/ContentView.vue';
import { AppStatus } from '@/enums';
import { mapState } from 'vuex';

export default {
  name: 'RankView',
  components: {
    [Icon.name]: Icon,
    ContentView,
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      activeTab: 'weekly',
      rankType: 'popularity',
      currentDate: '2024年2月',
      rankList: [],
      query: {
        sdate: '',
        edate: '',
        type: 'popularity',
      },
      dateRanges: getDateRanges(),
    };
  },
  computed: {
    ...mapState(['supportWebP']),
    otherRanks() {
      // 返回第4名及以后的排名
      return this.rankList.slice(3);
    },
  },
  created() {
    const { start: weekStart, end: weekEnd } = this.dateRanges.week;
    this.query.sdate = weekStart;
    this.query.edate = weekEnd;
    this.initData();
  },
  methods: {
    refresh() {
      this.initData();
    },
    reload() {
      this.initData();
    },
    initData() {
      this.fetchRankList();
    },
    fetchRankList() {
      getRankList(this.query)
        .then(res => {
          this.rankList = (res || []).map(item => ({
            ...item,
            ...item.datingUserVo,
          }));
          this.status = AppStatus.READY;
        })
        .catch(err => {
          this.status = AppStatus.ERROR;
          console.log(err);
        });
    },
    onScrollBottom() {
      // this.$emit('scroll-bottom');
    },
    switchTab(tab) {
      this.activeTab = tab;
      // 在实际应用中，这里可能需要请求不同的数据
      if (tab === 'weekly') {
        const { start: weekStart, end: weekEnd } = this.dateRanges.week;
        this.query.sdate = weekStart;
        this.query.edate = weekEnd;
      } else {
        const { start: monthStart, end: monthEnd } = this.dateRanges.month;
        this.query.sdate = monthStart;
        this.query.edate = monthEnd;
      }
      // 保持当前的榜单类型不变
      this.query.type = this.rankType;
      this.fetchRankList();
    },
    showDatePicker() {
      // 实现日期选择器的逻辑
      console.log('显示日期选择器');
    },
    formatNumber(num) {
      // 格式化数字，添加千位分隔符
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    getTrendClass(trend) {
      if (trend > 0) return 'trend-up';
      if (trend < 0) return 'trend-down';
      return 'trend-equal';
    },
    getImageUrl(url) {
      if (!url) {
        return '';
      }
      let image = url.split(',')[0];
      return image.startsWith('http')
        ? image
        : getImageURL(
            image,
            `?imageView2/1/w/240/h/240/format/${
              this.supportWebP ? 'webp' : 'jpg'
            }/q/90`
          );
    },
    goToUserDetail(id) {
      this.$router.push(`/blindDate/user/${id}`);
    },
    switchRankType(type) {
      this.rankType = type;
      this.query.type = type;
      this.fetchRankList();
    },
  },
};
</script>

<style lang="scss" scoped>
.ranking-container {
  width: 100%;
  height: 100%;
  padding-bottom: 66px;
  box-sizing: border-box;
  background-color: #f9fafb;
}

// 顶部标签和日期选择
.rank-type-header {
  padding: 12px;
  background: linear-gradient(135deg, #fff6f9, #ffffff);
  text-align: center;
  border-bottom: 1px solid #eee;

  .rank-type-tabs {
    display: inline-flex;
    background-color: #f2f2f2;
    border-radius: 8px;
    padding: 2px;

    .tab-item {
      padding: 6px 16px;
      font-size: 14px;
      border-radius: 6px;
      cursor: pointer;
      color: #666;

      &.active {
        background-color: #fff;
        color: #333;
        font-weight: 500;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

.ranking-sub-header {
  display: flex;
  justify-content: center;
  padding: 12px 0;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;

  .tabs {
    display: flex;
    background-color: #f2f2f2;
    border-radius: 8px;
    padding: 2px;

    .tab-item {
      padding: 6px 14px;
      font-size: 14px;
      color: #666;
      border-radius: 6px;
      cursor: pointer;

      &.active {
        background-color: #fff;
        color: #333;
        font-weight: 500;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .date-selector {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #333;
    cursor: pointer;

    .arrow-down {
      display: inline-block;
      width: 0;
      height: 0;
      margin-left: 5px;
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-top: 5px solid #666;
    }
  }
}

.top-three,
.rank-list {
  box-sizing: border-box;
  padding: 15px;
  background-color: #f9fafb;
}

// 前三名展示区域
.top-three {
  display: grid;
  grid-template-columns: 1fr 1.5fr 1fr;
  /* 第一名宽度为两侧的1.5倍 */
  grid-template-rows: 30px auto;
  /* 第一行放第一名的数字徽章，第二行放卡片内容 */
  grid-template-areas:
    '. first-badge .'
    'second first third';
  position: relative;
  padding: 0 15px 15px;
  column-gap: 12px;
  margin-top: 1px;
  /* 添加上边距与前面元素分隔 */
  /* 列间距 */

  .rank-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    border-radius: 10px;
    background: linear-gradient(135deg, #fff6f9 3%, #ffffff 99%);
    box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.08);
    padding: 15px 10px;
    transition: all 0.3s ease;
    /* 平滑过渡效果 */
    box-sizing: border-box;

    .rank-number {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 34px;
      height: 34px;
      border-radius: 50%;
      background-color: #b8b8b8;
      color: #fff;
      font-size: 16px;
      font-weight: bold;
      position: absolute;
      top: -17px;
      z-index: 2;
    }

    .rank-first-number {
      background-color: #ffb800;
      font-size: 20px;
      grid-area: first-badge;
      justify-self: center;
    }

    .avatar-container {
      width: 56px;
      height: 56px;
      border-radius: 50%;
      background-color: #e0ebe2;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      border: 2px solid #fff;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      margin-top: 12px;

      .avatar {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .name {
      margin-top: 10px;
      font-size: 16px;
      font-weight: 500;
      color: #333;
      text-align: center;
    }

    .score {
      font-size: 14px;
      color: #666;
      margin-top: 5px;

      .van-icon {
        margin-right: 5px;
      }
    }
  }

  .rank-first {
    grid-area: first;
    padding: 20px 10px 25px;
    height: 100%;
    box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.12);
    z-index: 2;
    align-self: start;
    /* 对齐到顶部 */

    .avatar-container {
      width: 80px;
      height: 80px;
      margin-top: 5px;
      border: 3px solid #fff;

      .avatar {
        width: 100%;
        height: 100%;
      }
    }

    .name {
      font-size: 18px;
      margin-top: 15px;
      font-weight: 600;
    }

    .score {
      font-size: 16px;
      font-weight: 500;
    }
  }

  .rank-second {
    grid-area: second;
    align-self: center;

    /* 对齐到底部 */
    .rank-number {
      background: #c0c0c0;
    }
  }

  .rank-third {
    grid-area: third;
    align-self: center;

    /* 对齐到底部 */
    .rank-number {
      background: #cd7f32;
    }
  }
}

.icon-rose {
  vertical-align: middle;
  color: #f83b57;
}

// 排名列表
.rank-list {
  margin-top: 1px;

  .rank-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;

    &:first-child {
      border-top-left-radius: 7px;
      border-top-right-radius: 7px;
    }

    &:last-child {
      border-bottom-left-radius: 7px;
      border-bottom-right-radius: 7px;
    }

    .rank-item-left {
      display: flex;
      align-items: center;

      .rank-number-list {
        width: 20px;
        font-size: 16px;
        color: #999;
        margin-right: 15px;
        text-align: center;
      }

      .avatar-small {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 12px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .user-info {
        .user-name {
          font-size: 16px;
          color: #333;
          margin-bottom: 4px;
        }

        .user-id {
          font-size: 12px;
          color: #999;
        }
      }
    }

    .rank-item-right {
      display: flex;
      align-items: center;

      .score {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-right: 8px;

        .van-icon {
          margin-right: 5px;
        }
      }

      .trend-icon {
        width: 16px;
        height: 16px;
        position: relative;

        &.trend-up:after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          border-left: 8px solid transparent;
          border-right: 8px solid transparent;
          border-bottom: 14px solid #ff5a5a;
        }

        &.trend-down:after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          border-left: 8px solid transparent;
          border-right: 8px solid transparent;
          border-top: 14px solid #4caf50;
        }

        &.trend-equal:after {
          content: '';
          position: absolute;
          top: 6px;
          left: 0;
          width: 16px;
          height: 2px;
          background-color: #999;
        }
      }
    }
  }
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  background-color: #fff;
  border-radius: 10px;
  margin: 15px;
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.05);

  .empty-icon {
    width: 80px;
    height: 80px;
    background-color: #f2f2f2;
    border-radius: 50%;
    margin-bottom: 20px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='48' height='48' viewBox='0 0 24 24' fill='none' stroke='%23cccccc' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='9' cy='7' r='4'%3E%3C/circle%3E%3Cpath d='M22 21v-2a4 4 0 0 0-3-3.87'%3E%3C/path%3E%3Cpath d='M16 3.13a4 4 0 0 1 0 7.75'%3E%3C/path%3E%3C/svg%3E");
    background-position: center;
    background-repeat: no-repeat;
    background-size: 48px;
  }

  .empty-text {
    font-size: 18px;
    color: #333;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .empty-subtext {
    font-size: 14px;
    color: #999;
  }
}
</style>
