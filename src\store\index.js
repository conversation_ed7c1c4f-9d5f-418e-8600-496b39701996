import Vue from 'vue';
import Vuex from 'vuex';

import quiz from './quiz';
import blindDate from './blindDate';

import * as getters from './getters';
import actions from './action';
import mutations from './mutations';
import state from './state';

Vue.use(Vuex);

const store = new Vuex.Store({
  state,
  getters,
  actions,
  mutations,
  modules: {
    quiz, // 添加 QuizService 模块
    blindDate, // 添加 BlindDate 模块
  },
});

export default store;
