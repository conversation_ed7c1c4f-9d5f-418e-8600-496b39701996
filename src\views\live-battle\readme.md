## 在线答题pk

### 项目基本信息

- 框架：Vue 2.6
- UI组件：Vant 2
- WebSocket通信：SockJS + Stomp
- 状态管理：Vuex

### 问答PK系统交互流程

#### 1. 连接阶段

- 使用SockJS建立WebSocket连接
- 订阅特定topic
- 等待对手进入游戏

#### 2. 游戏开始

- 服务端发送 `type: start`消息
- 消息包含:
  - 试题列表
  - 开始时间 `startTimestamp`
  - PK总时长 `totalDuration`
  - 游戏进度

#### 3. 答题机制

- 根据 `totalDuration`和题目数计算单题时间
- 显示每题倒计时
- 超时自动切换下一题
- 选手独立答题，无需等待对手

#### 4. 答题交互

- 每答完一题，服务端广播 `type: answer`消息
- 消息包含双方比分

#### 5. 游戏结束

- 双方答完所有题目
- 服务端发送 `type: end`结束消息

#### 6. 进度管理

- 进度保存在服务端
- 支持页面刷新后恢复答题进度状态

## 问题

1. 创建者进入房间后，刷新页面，如何重新连接
2. 参与者进入房间后，刷新页面，如何重新连接
3. 标签页失活，socket连接断开，（调整心跳间隔，页面恢复重新连接？）

## 待办

1. 答题过程中服务端断开socket
