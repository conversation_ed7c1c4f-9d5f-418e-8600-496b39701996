<template>
  <content-view :status="status" @reload="reload" @scroll-bottom="onScrollBottom">
    <div class="couples-view">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="couples-list-container">
        <van-list v-model="loading" :finished="finished" :finished-text="couplesList.length > 10 ? '没有更多了' : ''"
          @load="onLoad">
          <div class="couples-list">
            <!-- CP卡片 -->
            <couple-card v-for="(couple, index) in couplesList" :key="index" :couple="couple" :show-details="true"
              @click="viewCoupleDetail" />

            <!-- 无数据提示 -->
            <div v-if="couplesList.length === 0 && !loading" class="empty-state">
              <van-empty description="缘分未到, 暂无CP组合" />
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
  </content-view>
</template>

<script>
import { List, PullRefresh, Empty, Icon } from 'vant';
import ContentView from '@/components/ContentView.vue';
import CoupleCard from './CoupleCard.vue';
import { getImageURL } from '@/common/image';
import { AppStatus } from '@/enums';
import { getCpList } from '../api';
export default {
  name: 'CouplesView',
  components: {
    [List.name]: List,
    [PullRefresh.name]: PullRefresh,
    [Empty.name]: Empty,
    [Icon.name]: Icon,
    ContentView,
    CoupleCard
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      couplesList: [],
      loading: false,
      finished: false,
      refreshing: false,
      page: 1,
      pageSize: 10,
    };
  },
  created() {
    this.loadCouplesList();
  },
  methods: {
    // 加载CP列表
    loadCouplesList() {
      getCpList({ status: 1, page: this.page, pageSize: this.pageSize }).then(response => {
        if (this.refreshing) {
          this.couplesList = response.list;
          this.refreshing = false;
        } else {
          this.couplesList = [...this.couplesList, ...response.list];
        }

        this.loading = false;
        this.status = AppStatus.READY;
        // 模拟数据加载完成
        if (this.pageSize >= response.list.length) {
          this.finished = true;
        }
        this.page++;
      });
    },
    getImageUrl(url) {
      if (!url) {
        return '';
      }
      return getImageURL(url.split(',')[0]);
    },
    onLoad() {
      if (this.refreshing) {
        this.loading = false;
        return;
      };
      this.loadCouplesList();
    },
    onRefresh() {
      // 重置状态
      this.finished = false;
      this.loading = true;
      this.page = 1;
      // 重新加载数据
      this.loadCouplesList();
    },
    reload() {
      // this.status = AppStatus.LOADING;
      this.onRefresh();
      // this.status = AppStatus.READY;
    },
    onScrollBottom() {
      console.log('🚀 ~ onScrollBottom ~ onScrollBottom: CouplesView')
      // this.$emit('scroll-bottom');
    },
    viewCoupleDetail(couple) {
      if (!couple.id) return;
      this.$router.push({
        path: `/blindDate/couple/${couple.id}`,
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.couples-view {
  padding: 15px;
  background-color: #f5f6fa;
  // min-height: 100vh;
  box-sizing: border-box;
  height: 100%;

  .couples-list-container {
    min-height: 100%;
    box-sizing: border-box;
  }
}

.couples-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.empty-state {
  padding: 40px 0;
}
</style>
