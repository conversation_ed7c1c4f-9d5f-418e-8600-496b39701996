<template>
  <div class="tab-bar">
    <van-tabbar v-model="active" active-color="#FF4785" :before-change="beforeChange">
      <van-tabbar-item name="HomeView">
        <span>首页</span>
        <template #icon>
          <span class="icon_jglh icon-shouye"></span>
        </template>
      </van-tabbar-item>
      <van-tabbar-item name="RankView" icon="chart-trending-o">
        <span>榜单</span>
        <template #icon>
          <span class="icon_jglh icon-bangdan"></span>
        </template>
      </van-tabbar-item>
      <van-tabbar-item name="CouplesView">
        <span>有缘人</span>
        <template #icon>
          <van-icon name="like" />
        </template>
      </van-tabbar-item>
      <!-- <van-tabbar-item name="ApplyView" icon="add-o"> 报名 </van-tabbar-item> -->
      <van-tabbar-item name="ActivityView" icon="calendar-o">
        <span>活动</span>
        <template #icon>
          <span class="icon_jglh icon-baomingdengjitiaocha"></span>
        </template>
      </van-tabbar-item>
      <van-tabbar-item name="ProfileView" icon="user-o" :dot="unreadCount > 0">
        <span>我的</span>
        <template #icon>
          <span class="icon_jglh icon-wode2"></span>
        </template>
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script>
import { Tabbar, TabbarItem, Icon } from 'vant';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { mapState } from 'vuex';

export default {
  name: 'TabBar',
  mixins: [mixinAuthRouter, mixinShare],
  components: {
    [Tabbar.name]: Tabbar,
    [TabbarItem.name]: TabbarItem,
    [Icon.name]: Icon,
  },
  props: {
    value: {
      type: String,
      default: 'HomeView',
    },
    unreadCount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      active: 'HomeView',
      tabarNames: [
        'HomeView',
        'RankView',
        'CouplesView',
        'ActivityView',
        'ProfileView',
      ],
    };
  },
  computed: {
    ...mapState({
      profile: state => state.blindDate.profile,
    }),
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        this.active = val;
      },
    },
  },
  methods: {
    beforeChange(index) {
      if (index === 'ProfileView') {
        if (this.$_auth_isLoggedIn) {
          this.$emit('change', index);
        } else {
          this.$_auth_login();
          return false;
        }
      } else {
        this.active = index;
        this.$emit('change', index);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.tab-bar {
  z-index: 100;

  .icon_jglh {
    font-size: 18px;
  }
}
</style>
