<template>
  <div v-if="activities.length > 0" class="activity-banner">
    <swiper
      class="banner-swiper"
      ref="slider"
      :options="{ autoplay: true, delay: 3000 }"
      :pagination="false"
      :allow-empty="true"
      value-mode="index"
      v-model="currentSwiperIndex"
    >
      <swiper-item v-for="(activity, index) in activities" :key="index">
        <div
          class="banner-item"
          :style="{ backgroundImage: `url(${getImageUrl(activity.image)})` }"
          @click="handleActivityClick(activity)"
        ></div>
      </swiper-item>
      <div slot="extra" class="swiper-counts" v-if="activities.length > 1">
        {{ currentSwiperIndex + 1 }}/{{ activities.length }}
      </div>
    </swiper>
  </div>
</template>

<script>
import { Swiper, SwiperItem } from '@/components/Swiper';
import { getImageURL } from '@/common/image';
import { mixinAuthRouter, mixinShare } from '@/mixins';

export default {
  name: 'ActivityBanner',
  mixins: [mixinAuthRouter, mixinShare],
  components: {
    Swiper,
    SwiperItem,
  },
  props: {
    activities: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      currentSwiperIndex: 0,
    };
  },
  methods: {
    handleActivityClick(activity) {
      this.toActionsPage(activity);
    },
    toActionsPage(item) {
      // 跳转到报名详情页
      const path = `/actions/plugins/template/signup/?id=${item.actionId}&actionId=${item.actionId}`;
      if (!this.$_auth_isLoggedIn) {
        this.$_auth_login();
        return;
      }
      this.$_router_pageTo(`${location.origin}${path}`, {
        titleBar: true,
        shareButton: true,
      });
    },
    getImageUrl(url) {
      if (!url) {
        return '';
      }
      let image = url.split(',')[0];
      return image.startsWith('http') ? image : getImageURL(image);
    },
  },
};
</script>

<style lang="scss" scoped>
.activity-banner {
  position: relative;
  width: 100%;
  height: 120px;
  margin: 10px 0;
  border-radius: 8px;
  overflow: hidden;
}

.banner-swiper {
  height: 100%;
  width: 100%;
}

.banner-item {
  position: relative;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
}

.swiper-counts {
  position: absolute;
  right: 10px;
  bottom: 10px;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  z-index: 10;
}
</style>
