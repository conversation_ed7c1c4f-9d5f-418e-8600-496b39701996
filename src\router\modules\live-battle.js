import { handleError } from '../error-handler';

export default [
  // {
  //   path: '/live/battle/demo',
  //   name: 'liveBattle',
  //   component: resolve => {
  //     import(/* webpackChunkName: "wechat" */ '@/views/live-battle/demo.vue')
  //       .then(resolve)
  //       .catch(handleError);
  //   },
  // },
  {
    path: '/live/battle/home',
    name: 'OnlinePK',
    component: resolve => {
      import(
        /* webpackChunkName: "jglh-actions" */ '@/views/live-battle/index.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/live/battle/room',
    name: 'liveBattleHome',
    component: resolve => {
      import(/* webpackChunkName: "wechat" */ '@/views/live-battle/home.vue')
        .then(resolve)
        .catch(handleError);
    },
  },
];
