<template>
  <div class="promotional-goods">
    <biz-image class="goods-img" :src="prize.image" type="?imageView2/1/w/600/format/jpg/q/100" :lazy="false">
    </biz-image>
    <div class="goods-title">{{ prize.prizeName }}</div>
    <div class="goods-btn" @click="toGoodsDetail">
      <div class="price">
        <span class="num">￥{{ prize.lotteryAmount }}</span>
        <span class="prize-text">秒杀价</span>
      </div>
      <div class="right-text">立即抢购</div>
    </div>
  </div>
</template>

<script>
import { PrizeType as PrizeTypeEnum } from '@/enums';

export default {
  name: 'PrizePopPromotionalGoods',
  props: {
    prize: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      prizeType: PrizeTypeEnum.PROMOTIONAL_GOODS.valueOf()
    };
  },
  methods: {
    toGoodsDetail() {
      this.$emit('goodsDetail', this.prize);
    }
  }
};
</script>

<style lang="scss" scoped>
.promotional-goods {
  padding: 0;

  .goods-img {
    margin-bottom: 12px;
    width: 100%;
  }

  .goods-title {
    width: 100%;
    font-size: 18px;
    color: #111111;
    line-height: 24px;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 12px;
  }

  .goods-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    box-sizing: border-box;
    background: url(../../assets/images/gasha_btn_lightning.png) no-repeat,
      linear-gradient(270deg, #fd4925 0%, #fd1a41 100%);
    background-size: 26px 52px, auto;
    background-position: 138px 0, center;
    border-radius: 999px;
    padding: 0 15px;
    height: 52px;

    .price {
      display: inline-flex;
      align-items: center;

      .num {
        font-size: 18px;
        color: #ffffff;
      }

      .prize-text {
        font-size: 12px;
        color: #ffffff;
        margin-left: 8px;
      }
    }

    .right-text {
      font-size: 16px;
      color: #ffffff;
    }
  }
}
</style>
