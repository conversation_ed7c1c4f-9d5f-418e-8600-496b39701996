<template>
  <div class="discount-calculator">
    <div v-if="coupon" class="weui-cell weui-cell_select">
      <div class="weui-cell__hd">
        <label for="" class="weui-label">优惠券</label>
      </div>
      <div class="weui-cell__bd">
        <div class="align-right" @click="goSelectTicket">
          <template v-if="ticket">
            <div class="ticket discount">
              -<span class="rmb">{{ formatMoney(ticketAmount) }}</span>
            </div>
          </template>
          <div v-else class="ticket">
            <span class="ticket-count" v-if="ticket === 0"><b>{{ ticketCount }}</b>张可用</span>
            <span class="ticket-empty" v-else-if="ticketCount === -1">选择优惠券</span>
            <span class="ticket-count" v-else-if="ticketCount > 0"><b>{{ ticketCount }}</b>张</span>
            <span class="ticket-empty" v-else>没有可用的优惠券</span>
          </div>
        </div>
      </div>
    </div>
    <div v-if="accountBalance > 0 && redpocket" class="weui-cell weui-cell-redpocket">
      <div class="weui-cell__hd">
        <label class="weui-label">红包<span class="redpocket-extra">余额{{ formatMoney(accountBalance) }}元</span></label>
      </div>
      <div class="weui-cell__bd">
        <div v-if="avaliableBalanceAmount" class="account-amount">
          <span>可抵扣</span>
          <span class="rmb rmb-redpocket">{{
            formatMoney(avaliableBalanceAmount)
            }}</span>
          <!-- <input type="checkbox" v-model="useAccountBalance"> -->
          <!-- <label
              class="weui-cells_radio weui-check__label"
            >
              <input type="checkbox" class="weui-check" v-model="useAccountBalance">
              <span class="weui-icon-checked" @click.stop.prevent="toggle"></span>
            </label> -->
          <label class="weui-cell_switch">
            <input class="weui-switch" type="checkbox" v-model="useAccountBalance" />
          </label>
        </div>
        <div v-else class="account-amount">不可用</div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatDate, formatMoney } from '@/utils';
import { AppStatus, Ticket } from '@/enums';
import { login } from '@/bridge';
import { isInJglh, isInWeixin } from '@/common/env';
import { dialog, toast, loading, back, onSelectTicket } from '@/bus';
import { getUserAccountInfo } from '@/api/modules/account';
import { selectOrderTicketList } from '@/api/modules/ticket';

function getData(params) {
  const requests = [selectOrderTicketList(params), getUserAccountInfo()];
  return Promise.all(requests);
}

/**
 * 通用下单选券，选择红包抵扣组件
 * @property {Number}   accountBalance  红包余额
 * @property {Boolean}  coupon  是否显示优惠券 默认true
 * @property {Boolean}  redpocket  是否显示红包抵扣 默认true
 * @property {Number}   ticketCount  可用优惠券数量
 * @property {Number}   price  商品总价
 * @property {Object}   ticketQuery  优惠券查询参数
 * @property {Object}   maxTicket  最大可用优惠券 商城商品下单时，自动选择最大可用优惠券
 * @property {Boolean}  useVipCoupon  是否可以使用组合开通会员赠送的专享优惠券 默认false
 * @property {Boolean}  category  vip类型 默认mall, car
 */
export default {
  name: 'OrderDiscountCalculator',
  props: {
    accountBalance: {
      type: Number,
      default: 0,
    },
    ticketCount: {
      type: Number,
      default: -1,
    },
    price: {
      type: Number,
      default: 0,
    },
    ticketQuery: {
      type: Object,
      default() {
        return {};
      },
    },
    maxTicket: {
      type: Object,
      default() {
        return {};
      },
    },
    redpocket: {
      type: Boolean,
      default: true,
    },
    coupon: {
      type: Boolean,
      default: true,
    },
    useVipCoupon: {
      type: Boolean,
      default: false,
    },
    category: {
      type: String,
      default: 'mall',
    },
  },
  components: {},
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      useAccountBalance: false,
      page: {
        tickets: [],
        account: null,
      },
      // ticket: null,
      vipTicket: null,
      normalTicket: null,
    };
  },
  computed: {
    tickets() {
      return this.page.tickets;
    },
    ticket() {
      if (this.vipTicket) {
        return this.vipTicket
      }
      return this.normalTicket;
    },
    // 优先使用优惠券支付，剩余部分用红包余额抵扣
    avaliableBalanceAmount() {
      const accountBalance = this.accountBalance;
      if (this.price === 0 || accountBalance === 0) return 0;

      // ********添加逻辑
      // 如果允许使用优惠券，优先抵扣优惠券，如果不允许则忽略优惠券金额
      let preComputeAmount = this.price;
      if (this.coupon) {
        preComputeAmount = this.price - this.ticketAmount; // 减去优惠券金额
      } else {
        preComputeAmount = this.price;
      }
      // 如果不允许使用红包，则直接返回0
      if (!this.redpocket) return 0;
      // 订单金额被优惠券完全抵扣（目前业务，优惠券金额大于订单金额时也可使用）
      if (preComputeAmount <= 0) return 0;

      const amount2 = preComputeAmount - accountBalance; // 减去红包余额

      return amount2 < 0 ? preComputeAmount : accountBalance;
    },
    // 使用的优惠券金额
    ticketAmount() {
      let amount = this.price;
      const ticket = this.ticket;
      if (ticket && this.coupon) {
        if (ticket.type == Ticket.DISCOUNT) {
          return amount - ticket.value * amount;
        } else {
          if (ticket.value > amount) return amount;
          return ticket.value;
        }
        // if (ticket.type == Ticket.COUPON) return amount - ticket.value;
      }
      return 0;
    },
    totalDiscountAmount() {
      const usingBalanceAmount = this.useAccountBalance
        ? this.avaliableBalanceAmount
        : 0;
      return this.ticketAmount + usingBalanceAmount;
    },
    modelData() {
      return {
        redPocket: this.useAccountBalance,
        ticket: (this.normalTicket && this.normalTicket.id) || null,
        vipTicket: (this.vipTicket && this.vipTicket.couponId) || null,
        discountAmount: this.totalDiscountAmount,
      };
    },
  },
  watch: {
    modelData(val) {
      this.emitInput();
    },
    ticketCount(val) {
      this.setSelectedTicket();
    },
    maxTicket(val) {
      this.setSelectedTicket();
    },
  },
  mounted() {
    onSelectTicket((item) => {
      // 开会员赠送的优惠券才会有 couponId, 参见TicketSelect.vue>computed>renameVipCoupon
      if (this.useVipCoupon && item.couponId) {
        this.vipTicket = item;
        this.normalTicket = null;
        return;
      }
      this.normalTicket = item;
      this.vipTicket = null;
    });
    this.setSelectedTicket();
    this.emitInput();
    // this.init();
  },
  methods: {
    ...{ formatMoney },
    // 没用，获取可用优惠券再mixin中
    // getPageData() {
    //   getData(this.ticketQuery).then(([tickets, accountInfo]) => {
    //     this.page.tickets = tickets;
    //     this.page.account = accountInfo;
    //   }).catch(e => {
    //     console.error(e);
    //     e && toast().tip(String(e));
    //   });
    //   this.status = AppStatus.READY;
    // },
    emitInput() {
      this.$emit('input', this.modelData);
    },
    setSelectedTicket() {
      // 默认选中最大优惠券
      if (Object.keys(this.maxTicket).length > 0 && this.coupon) {
        if (this.maxTicket.couponId) {
          this.vipTicket = {
            id: this.maxTicket.couponId,
            couponId: this.maxTicket.couponId,
            type: this.maxTicket.type,
            value: this.maxTicket.amount || (this.maxTicket.reelDiscount / 10),
            minOrderAmount: this.maxTicket.restrictionsAmount || 0,
            couponName: this.maxTicket.couponName,
            amount: this.maxTicket.amount,
            restrictionsAmount: this.maxTicket.restrictionsAmount
          };
          this.normalTicket = null
        } else {
          this.normalTicket = {
            id: this.maxTicket.id,
            image: this.maxTicket.advertisementImage,
            startTime: this.maxTicket.validTime,
            endTime: this.maxTicket.loseTime,
            type: this.maxTicket.reelType,
            name: this.maxTicket.reelName,
            value: this.maxTicket.reelAmount || this.maxTicket.reelDiscount / 10,
            minOrderAmount: this.maxTicket.restrictionsAmount || 0,
            bizLimit: this.maxTicket.pName, // 业务限制
            shopLimit: this.maxTicket.bName, // 商家限制
            status: this.maxTicket.status,
            couponName: this.maxTicket.reelName,
            rule: this.maxTicket.rule,
            amount: this.maxTicket.reelAmount,
            reelType: this.maxTicket.reelType,
            restrictionsAmount: this.maxTicket.restrictionsAmount,
          };
          this.vipTicket = null
        }
      } else {
        this.normalTicket = null;
        this.vipTicket = null;
      }
    },
    toggle(e) {
      // console.log('toggle', e);
      this.useAccountBalance = !this.useAccountBalance;
    },
    goSelectTicket() {
      if (!this.ticketCount) return;
      let selected = this.ticket;
      if (this.ticket) {
        selected = this.ticket.id || this.ticket.couponId;
      } else if (this.ticket === 0) {
        selected = 0;
      }
      this.$router.push({
        name: 'ticket-select',
        params: {
          ...this.ticketQuery,
          selected,
          useVipCoupon: this.useVipCoupon,
          vipCategory: this.category,
        },
      });
    },
    init() {
      this.getPageData();
    },
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
$item-border-color: #efefef;

.discount-calculator {
  // @include border-top($item-border-color);
  border-top: 1px solid #efefef;

  .ios & {
    border-top-width: 0.5px;
  }
}

.weui-cell-redpocket {

  // padding-top: 12px;
  // padding-bottom: 12px;
  .weui-label {
    width: 150px;
  }

  .redpocket-extra {
    display: block;
    font-size: 12px;
    color: gray;
    word-break: keep-all;
  }

  .weui-cell__hd {
    flex: 1;

    .weui-label {
      width: 100%;
    }
  }
}

.weui-check__label {
  font-size: 0;

  &:active {
    background-color: transparent;
  }

  .weui-icon-checked {
    vertical-align: bottom;
  }
}

/* switch样式重写 */
.weui-cell_switch {
  padding-right: 0;
  border-top: 0;
  padding-top: 12px;
  padding-bottom: 12px;
  padding-left: 4px;
  font-size: 0;

  /* 去除html换行导致的多余间距 */
  .weui-switch {
    height: 26px;

    &::before,
    &::after {
      width: 24px;
      height: 24px;
    }

    &:checked::after {
      transform: translateX(25px);
    }
  }
}

.weui-checkbox {
  display: inline-block;
}

.weui-switch:checked {
  background-color: #fd4925;
  border-color: #fd4925;
}

.account-amount {
  text-align: right;
  display: flex;
  justify-content: flex-end;
  align-items: center;

  span {
    word-break: keep-all;
  }
}

.rmb-redpocket {
  color: #fd4925;
  margin: 0 2px;
}

.ticket-empty {
  color: #ccc;
}

.discount {
  color: #fd4925;
  font-size: 18px;
}

.rmb {
  font-size: 18px;

  &::before {
    font-size: 0.8em;
  }
}

.mprice {
  text-decoration: line-through;
  margin-right: 10px;
  color: gray;
  font-size: 16px;
}

.order-amount {
  color: #131312;
  font-size: 14px;

  .rmb {
    font-size: 18px;
    font-weight: 700;
  }
}

.weui-cell__vip {
  color: #fd4925;

  .discount {
    color: #fd4925;
  }
}

.ticket {
  margin-right: 24px;

  .ticket-count {
    background: #fb521d;
    color: white;
    font-size: 13px;
    padding: 1px 3px;
    border-radius: 2px;
    /* px */
  }

  .ticket-detail {
    font-size: 0.8em;
    color: #6f6f6f;
  }
}
</style>
