<template>
  <container @ready="init" class="bind-wx-pub">
    <x-header title="绑定微信">
      <x-button slot="left" type="back"></x-button>
    </x-header>

    <content-view :status="status" @reload="reload">
      <template v-if="status === AppStatus.READY">
        <div class="wrap">
          <transition name="fade-up" appear>
            <div class="form-card">
              <div class="form-header">
                <div class="icon-wrapper">
                  <van-icon name="wechat" size="64" color="#07c160" />
                </div>
                <h2 class="form-title">审车商家绑定交广领航微信公众号</h2>
              </div>

              <!-- 步骤指示器 -->
              <div class="step-indicator">
                <van-steps :active="currentStep" active-color="#FD4925">
                  <van-step>查询手机号</van-step>
                  <van-step>选择商家</van-step>
                  <van-step>验证绑定</van-step>
                </van-steps>
              </div>

              <div class="form-content">
                <!-- 步骤1：输入小秘书手机号并查询 -->
                <div v-show="currentStep === 0" class="step-content">
                  <van-field class="phone-field" v-model="phone" type="tel" maxlength="11" label="小秘书手机号"
                    placeholder="请输入小秘书手机号" :rules="[{ required: true, message: '请输入手机号' }]" />

                  <div class="form-footer">
                    <van-button type="primary" size="small" :disabled="!isValidPhone" @click="queryBusinessList"
                      class="submit-btn" :loading="queryLoading">
                      查询商家
                    </van-button>
                  </div>

                  <div class="tips">
                    请输入小秘书手机号，查询关联的商家列表
                  </div>
                </div>

                <!-- 步骤2：选择商家 -->
                <div v-show="currentStep === 1" class="step-content">
                  <div class="business-list">
                    <div class="business-list-title">
                      请选择需要绑定的商家
                      <span v-if="selectedBusinessIds.length > 0" class="selected-count">
                        (已选择{{ selectedBusinessIds.length }}个)
                      </span>
                    </div>

                    <van-checkbox-group v-model="selectedBusinessIds" class="business-checkbox-group">
                      <van-cell-group>
                        <van-cell v-for="item in businessList" :key="item.id" :title="item.name" :label="item.address"
                          clickable @click="toggleBusinessSelection(item.id)">
                          <template #icon>
                            <div class="business-logo">
                              <van-icon v-if="!item.logo" name="shop-o" size="24" />
                              <biz-image v-else class="shop-logo" :src="item.logo">
                              </biz-image>
                            </div>
                          </template>
                          <template #right-icon>
                            <van-checkbox :name="item.id" shape="square" checked-color="#FD4925" />
                          </template>
                        </van-cell>
                      </van-cell-group>
                    </van-checkbox-group>

                    <div class="empty-result" v-if="!businessList.length">
                      未查询到关联商家
                    </div>
                  </div>

                  <div class="form-footer">
                    <div class="button-group">
                      <van-button plain size="small" @click="backToStep(0)" class="back-btn">
                        返回
                      </van-button>
                      <van-button type="primary" size="small" :disabled="selectedBusinessIds.length === 0"
                        @click="goToStep3" class="next-btn">
                        下一步({{ selectedBusinessIds.length }})
                      </van-button>
                    </div>
                  </div>
                </div>

                <!-- 步骤3：验证码绑定 -->
                <div v-show="currentStep === 2" class="step-content">
                  <div class="selected-businesses" v-if="selectedBusinesses.length > 0">
                    <div class="selected-business-title">已选择商家 ({{ selectedBusinesses.length }}个)</div>
                    <div class="selected-business-list">
                      <div v-for="business in selectedBusinesses" :key="business.id" class="selected-business-item">
                        <div class="business-logo">
                          <van-icon v-if="!business.logo" name="shop-o" size="20" />
                          <biz-image v-else class="shop-logo" :src="business.logo">
                          </biz-image>
                        </div>
                        <div class="business-detail">
                          <div class="business-name">{{ business.name }}</div>
                          <div class="business-address">{{ business.address }}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <van-field v-model="phone" type="tel" maxlength="11" label="手机号" placeholder="请输入手机号" disabled
                    :rules="[{ required: true, message: '请输入手机号' }]" />

                  <van-field v-model="smsCode" type="number" maxlength="6" label="验证码" placeholder="请输入验证码"
                    :rules="[{ required: true, message: '请输入验证码' }]">
                    <template #button>
                      <van-button size="small" type="primary" :disabled="!canSendSms || smsCountdown > 0"
                        @click="sendSms" class="sms-btn">
                        {{ smsCountdown > 0 ? `${smsCountdown}s后重发` : '获取验证码' }}
                      </van-button>
                    </template>
                  </van-field>

                  <div class="form-footer">
                    <div class="button-group">
                      <van-button plain size="small" @click="backToStep(1)" class="back-btn">
                        返回
                      </van-button>
                      <van-button type="primary" size="small" :disabled="submitDisabled" @click="submit"
                        class="next-btn" :loading="submitLoading">
                        绑定
                      </van-button>
                    </div>
                  </div>

                  <div class="tips">
                    绑定成功后有新订单可以通过交广领航服务号接收新订单通知
                  </div>
                </div>
              </div>
            </div>
          </transition>
        </div>

        <!-- 优化后的成功弹窗 -->
        <van-dialog v-model="showDialog" title="绑定成功" :show-cancel-button="false" confirm-button-text="我知道了"
          class="success-dialog" @confirm="showDialog = false" :beforeClose="beforeClose">
          <div class="success-content">
            <van-icon name="success" size="56" color="#07c160" />
            <p class="success-message">恭喜您，微信公众号绑定成功！</p>
            <p class="success-tips">有新订单后可以通过交广领航服务号接收新订单通知</p>
          </div>
        </van-dialog>
      </template>
    </content-view>
  </container>
</template>

<script>
// import { Container, ContentView, Header, HeaderButton } from '@/components';
import { Field, Button, Dialog, Icon, Step, Steps, CheckboxGroup, Checkbox, Cell, CellGroup } from 'vant';
import { AppStatus } from '@/common/enums';
import { toast } from '@/bus';
import { getBusinessInfoBySecretaryPhone, bindWxPubBySecretaryPhone } from './api/index';
import { getPhoneCode } from '@/api/modules/account';

export default {
  name: 'BindWxPubVehicle',
  components: {
    [Field.name]: Field,
    [Button.name]: Button,
    [Dialog.Component.name]: Dialog.Component,
    [Icon.name]: Icon,
    [Step.name]: Step,
    [Steps.name]: Steps,
    [CheckboxGroup.name]: CheckboxGroup,
    [Checkbox.name]: Checkbox,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      phone: '', // 小秘书手机号
      smsCode: '', // 验证码
      showDialog: false,
      openId: '', // 从URL参数获取
      smsCountdown: 0, // 验证码倒计时
      smsTimer: null, // 倒计时定时器
      currentStep: 0, // 当前步骤 0:查询手机号 1:选择商家 2:验证码绑定
      businessList: [], // 查询到的商家列表
      selectedBusinessIds: [], // 选择的商家ID数组（多选）
      queryLoading: false, // 查询按钮loading状态
      submitLoading: false, // 绑定按钮loading状态
    };
  },
  computed: {
    // 是否有效手机号
    isValidPhone() {
      return this.phone && /^1[0-9]{10}$/.test(this.phone);
    },

    // 是否可以发送验证码
    canSendSms() {
      return this.selectedBusinessIds.length > 0 && this.isValidPhone && this.currentStep === 2;
    },

    // 获取选中的商家对象数组
    selectedBusinesses() {
      return this.businessList.filter(item => this.selectedBusinessIds.includes(item.id));
    },

    // 绑定按钮是否禁用
    submitDisabled() {
      return this.selectedBusinessIds.length === 0 || !this.phone || !this.smsCode || this.smsCode.length !== 6;
    }
  },
  watch: {
    phone(val) {
      // 限制输入长度
      if (val.length > 11) {
        this.phone = this.phone.slice(0, 11);
      }
    },
    smsCode(val) {
      // 限制输入长度
      if (val.length > 6) {
        this.smsCode = this.smsCode.slice(0, 6);
      }
    },
  },
  mounted() {
    // 获取URL参数中的openId
    const urlParams = new URLSearchParams(window.location.search);
    this.openId = urlParams.get('openId') || '';
  },
  beforeDestroy() {
    // 清理定时器
    if (this.smsTimer) {
      clearInterval(this.smsTimer);
      this.smsTimer = null;
    }
  },
  methods: {
    init() {
      this.status = AppStatus.READY;
    },

    // 发送验证码
    async sendSms() {
      if (!this.canSendSms) {
        toast().tip('请先选择商家');
        return;
      }

      try {
        const result = await getPhoneCode({
          phone: this.phone,
        });

        if (result.value === 0) {
          toast().tip('验证码已发送');

          // 开始倒计时
          this.smsCountdown = 60;
          this.smsTimer = setInterval(() => {
            this.smsCountdown--;
            if (this.smsCountdown <= 0) {
              clearInterval(this.smsTimer);
              this.smsTimer = null;
            }
          }, 1000);
        } else {
          toast().tip(result.msg || '发送验证码失败');
        }
      } catch (err) {
        toast().tip(err.message || '发送验证码失败');
      }
    },
    async submit() {
      if (!this.openId) {
        toast().tip('未获取到openId');
        return;
      }

      if (this.selectedBusinessIds.length === 0) {
        toast().tip('请选择要绑定的商家');
        return;
      }

      if (!this.phone) {
        toast().tip('请输入手机号');
        return;
      }

      if (!this.smsCode) {
        toast().tip('请输入验证码');
        return;
      }

      if (this.smsCode.length !== 6) {
        toast().tip('请输入6位验证码');
        return;
      }

      try {
        this.submitLoading = true;
        await bindWxPubBySecretaryPhone({
          openid: this.openId,
          phone: this.phone,
          code: this.smsCode,
          bids: this.selectedBusinessIds,
        });
        this.showDialog = true;
      } catch (err) {
        toast().tip(err.message || '绑定失败');
      } finally {
        this.submitLoading = false;
      }
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    // 查询小秘书关联的商家列表
    async queryBusinessList() {
      if (!this.isValidPhone) {
        toast().tip('请输入正确的手机号');
        return;
      }

      try {
        this.queryLoading = true;

        // 使用接口文档中的API获取商家列表
        const result = await getBusinessInfoBySecretaryPhone({
          phone: this.phone
        });
        if (result) {
          this.businessList = result || [];
          this.selectedBusinessIds = []

          if (this.businessList.length > 0) {
            this.currentStep = 1; // 进入选择商家步骤
          } else {
            toast().tip('未查询到关联商家');
          }
        } else {
          toast().tip(result.msg || '查询商家失败');
        }
      } catch (err) {
        toast().tip(err.msg || '查询商家失败');
      } finally {
        this.queryLoading = false;
      }
    },
    backToStep(step) {
      this.currentStep = step;
    },
    goToStep3() {
      if (this.selectedBusinessIds.length === 0) {
        toast().tip('请至少选择一个商家');
        return;
      }

      this.currentStep = 2;
    },
    // 切换商家选择状态
    toggleBusinessSelection(businessId) {
      const index = this.selectedBusinessIds.indexOf(businessId);
      if (index > -1) {
        // 如果已选中，则取消选择
        this.selectedBusinessIds.splice(index, 1);
      } else {
        // 如果未选中，则添加到选择列表
        this.selectedBusinessIds.push(businessId);
      }
    },
    beforeClose(action, done) {
      this.showDialog = false;
      this.phone = '';
      this.smsCode = '';
      this.businessList = [];
      this.selectedBusinessIds = [];
      this.currentStep = 0;
      this.smsCountdown = 0;
      // 清理定时器
      if (this.smsTimer) {
        clearInterval(this.smsTimer);
        this.smsTimer = null;
      }
      done();
    },
  },
};
</script>

<style lang="scss" scoped>
.bind-wx-pub {
  background: linear-gradient(135deg, #f5f7fa 0%, #e4eaef 100%);
  min-height: 100vh;

  // 添加动画相关样式
  .fade-up-enter-active,
  .fade-up-leave-active {
    transition: opacity 0.4s ease, transform 0.4s ease;
  }

  .fade-up-enter-from,
  .fade-up-leave-to {
    opacity: 0;
    transform: translateY(20px);
  }

  .wrap {
    min-height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15px;
    box-sizing: border-box;
  }

  .form-card {
    background-color: #fff;
    border-radius: 16px;
    padding: 15px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
    width: 100%;
    max-width: 400px;
    box-sizing: border-box;

  }

  .form-header {
    margin-bottom: 15px;
    text-align: center;

    .icon-wrapper {
      margin-bottom: 15px;
      display: flex;
      justify-content: center;

      .van-icon {
        background-color: rgba(7, 193, 96, 0.1);
        padding: 12px;
        border-radius: 50%;
      }
    }

    .form-title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
      margin: 0;
    }
  }

  .step-indicator ::v-deep {
    margin-bottom: 20px;

    .van-step__title {
      font-size: 12px;
    }
  }

  .form-content ::v-deep {
    width: 100%;
    margin-bottom: 30px;

    .van-field {
      background: #f8fafc;
      border-radius: 12px;
      padding: 15px;
      margin-bottom: 16px;
      border: 1px solid transparent;
      transition: all 0.3s ease;

      &:last-child {
        margin-bottom: 0;
      }

      .van-field__label {
        color: #333;
        font-weight: 500;
        font-size: 14px;
        width: 56px;
      }

      .van-field__value {
        position: relative;
        flex: 1;

        .van-field__body {
          display: flex;

          .van-field__control {
            flex: 1;
            color: #333;
            font-size: 15px;

            &::placeholder {
              color: #bbb;
            }
          }
        }
      }

      .van-field__right-icon {
        margin-right: 0;
      }

      // 验证码输入框特殊样式
      .van-field__button {
        padding-left: 8px;
        flex-shrink: 0;
        max-width: 95px;
        font-size: 0;
      }

      // 验证码输入框特定样式
      .van-field__control {
        min-width: 80px;
      }

      &.phone-field {
        .van-field__label {
          width: 90px;
        }
      }
    }

    .sms-btn {
      height: 24px;
      padding: 0 10px;
      font-size: 12px;
      border-radius: 18px;
      min-width: 80px;
      background: #FD4925;
      border-color: #FD4925;
      transition: opacity 0.3s;
      white-space: nowrap;

      &:active {
        opacity: 0.8;
      }

      &.van-button--disabled {
        opacity: 0.5;
        background: #f2f3f5;
        border-color: #f2f3f5;
        color: #969799;
      }
    }
  }

  .form-footer {
    width: 100%;
    margin-bottom: 20px;
  }

  .submit-btn {
    width: 100%;
    height: 50px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 500;
    background: #FD4925;
    border-color: #FD4925;
    transition: all 0.3s ease;
    box-shadow: 0 6px 12px rgba(253, 73, 37, 0.2);

    &:active {
      transform: translateY(2px);
      box-shadow: 0 2px 6px rgba(253, 73, 37, 0.2);
    }

  }

  .tips {
    padding: 0 20px;
    font-size: 14px;
    color: #666;
    text-align: center;
    line-height: 1.5;
    margin-top: 10px;
  }

  // 全局对话框样式
  ::v-deep .van-dialog {
    border-radius: 16px;
    overflow: hidden;

    .van-dialog__header {
      padding-top: 24px;
      font-weight: 600;
      font-size: 18px;
    }

    .van-dialog__content {
      padding: 24px;
      color: #333;
      font-size: 15px;
      line-height: 1.6;
    }

    .van-dialog__footer {
      padding-bottom: 12px;
    }

    .van-button {
      font-size: 15px;
      font-weight: 500;
      border-radius: 20px;
      height: 40px;
      line-height: 38px;
      margin: 0 12px 12px;
    }

    .van-dialog__confirm {
      color: #fff;
      background: #FD4925;
      border-color: #FD4925;
    }
  }

  .business-list {
    margin-bottom: 20px;

    .business-list-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 10px;

      .selected-count {
        font-size: 14px;
        color: #FD4925;
        font-weight: normal;
      }
    }

    .business-checkbox-group {

      // 针对van-cell-group的样式
      ::v-deep .van-cell-group {
        background-color: transparent;

        .van-cell {
          margin-bottom: 10px;
          border-radius: 8px;
          padding: 12px 16px;
          background-color: #fff;
          position: relative;
          transition: all 0.3s ease;

          &:last-child {
            margin-bottom: 0;
          }

          &:hover {
            background-color: #f2f3f5;
          }

          &::after {
            display: none;
          }

          .van-cell__title {
            flex: 1;
            overflow: hidden;

            .van-cell__label {
              color: #666;
              margin-top: 4px;
            }
          }

          .business-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;

            .shop-logo {
              width: 40px;
              height: 40px;
              border-radius: 50%;
            }
          }
        }
      }
    }

    .empty-result {
      text-align: center;
      color: #666;
      font-size: 14px;
      margin-top: 10px;
    }
  }

  .button-group {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .back-btn,
    .next-btn {
      width: 48%;
      height: 50px;
      border-radius: 25px;
      font-size: 16px;
      font-weight: 500;
      transition: all 0.3s ease;

      &:active {
        transform: translateY(2px);
      }
    }

    .back-btn {
      background-color: #f2f3f5;
      border-color: #f2f3f5;
      color: #333;
    }

    .next-btn {
      background-color: #FD4925;
      border-color: #FD4925;
      color: #fff;
    }
  }
}

.selected-businesses {
  background: #f8fafc;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 16px;

  .selected-business-title {
    font-size: 14px;
    color: #333;
    font-weight: 500;
    margin-bottom: 10px;
  }

  .selected-business-list {
    .selected-business-item {
      display: flex;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #eee;

      &:last-child {
        border-bottom: none;
        padding-bottom: 0;
      }

      &:first-child {
        padding-top: 0;
      }

      .business-logo {
        margin-right: 10px;
        flex-shrink: 0;

        .shop-logo {
          width: 32px;
          height: 32px;
          border-radius: 50%;
        }
      }

      .business-detail {
        flex: 1;
        min-width: 0;

        .business-name {
          font-size: 13px;
          font-weight: 500;
          color: #333;
          margin-bottom: 3px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .business-address {
          font-size: 11px;
          color: #666;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}

.success-dialog {
  ::v-deep .van-dialog {
    border-radius: 16px;
    overflow: hidden;

    .van-dialog__header {
      padding-top: 24px;
      font-weight: 600;
      font-size: 18px;
      color: #333;
    }
  }
}

.success-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;

  .van-icon {
    margin-bottom: 16px;
    background-color: rgba(7, 193, 96, 0.1);
    padding: 12px;
    border-radius: 50%;
  }

  .success-message {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin: 0 0 8px;
  }

  .success-tips {
    font-size: 14px;
    color: #666;
    margin: 0;
    line-height: 1.5;
  }
}
</style>
