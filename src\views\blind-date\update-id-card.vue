<template>
  <container @ready="onReady" @leave="onLeave" @resume="onResume">
    <x-header title="更新身份证信息">
      <x-button slot="left" type="back" />
    </x-header>
    <content-view :status="status" @reload="init">
      <template v-if="status === AppStatus.READY">
        <div class="update-container">
          <div class="info-tip">
            <van-icon name="info-o" color="#f87c98" />
            <span>请填写正确的身份信息，更新后会重新审核</span>
          </div>

          <van-form @submit="submitForm" @failed="onFailed" :show-error-message="false">
            <div class="block-card">
              <div class="section-title">身份信息</div>

              <van-field v-model="formData.name" name="姓名" label="姓名" placeholder="请输入姓名"
                :rules="[{ required: true, message: '请填写姓名' }]" required />

              <van-field v-model="formData.idCardNumber" name="身份证号" label="身份证号" maxlength="18" placeholder="请输入身份证号码"
                :rules="[
                  { required: true, message: '请填写身份证号' },
                  {
                    pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
                    message: '身份证号格式错误',
                  },
                ]" required />
            </div>

            <div class="block-card">
              <div class="section-title">上传身份证照片</div>

              <div class="photo-upload-section">
                <div class="photo-label">身份证照片</div>
                <div class="id-card-uploads">
                  <biz-image-upload class="uploader-photo id-card-photo van-clearfix" ref="idCardFrontUploader"
                    :auto-upload="false" :multiple="false" :max-files="1" :upload-url="uploadUrl" uploadName="data"
                    bed="custom" :onUploaded="handleIdCardFrontUploaded" :onPreview="handleIdCardPreview"
                    :image-url-getter="imageUrlGetter" v-model="idCardFront">
                    <div class="upload-box" slot="uploadIconSlot">
                      <van-icon name="plus" size="24" />
                      <div>上传身份证正面</div>
                    </div>
                  </biz-image-upload>

                  <biz-image-upload class="uploader-photo id-card-photo van-clearfix" ref="idCardBackUploader"
                    :auto-upload="false" :multiple="false" :max-files="1" :upload-url="uploadUrl" uploadName="data"
                    bed="custom" :onUploaded="handleIdCardBackUploaded" :onPreview="handleIdCardPreview"
                    :image-url-getter="imageUrlGetter" v-model="idCardBack">
                    <div class="upload-box" slot="uploadIconSlot">
                      <van-icon name="plus" size="24" />
                      <div>上传身份证反面</div>
                    </div>
                  </biz-image-upload>
                </div>
              </div>
            </div>

            <div class="button-container">
              <van-button round block type="primary" native-type="submit" color="#FF6AA8"
                :loading="isLoading">提交更新</van-button>
            </div>
          </van-form>
        </div>
      </template>
    </content-view>
  </container>
</template>

<script>
import { AppStatus } from '@/enums';
import { Form, Field, Button, Toast, Icon, Uploader } from 'vant';
import { getProfile, updateIdCardInfo } from './api';
import { playPhotos } from '@/bridge';
export default {
  name: 'UpdateIdCardPage',
  components: {
    [Form.name]: Form,
    [Field.name]: Field,
    [Button.name]: Button,
    [Icon.name]: Icon,
    [Uploader.name]: Uploader,
    [Toast.name]: Toast,
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      formData: {
        id: '', // 用户ID
        name: '', // 姓名
        idCardNumber: '', // 身份证号
      },
      // 上传图片相关
      uploadUrl: '/resource/upload',
      idCardFront: [],
      idCardBack: [],
      isLoading: false,
      isBacking: false,
    };
  },
  methods: {
    onReady() {
      this.init();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    onResume() {
      this.init();
    },
    init() {
      this.fetchUserData();
    },
    // 获取用户数据
    fetchUserData() {
      Toast.loading({
        message: '加载数据中...',
        forbidClick: true,
      });

      getProfile()
        .then(res => {
          Toast.clear();

          if (res) {
            this.fillFormData(res);
            this.status = AppStatus.READY;
          } else {
            Toast('获取数据失败，请重试');
            this.status = AppStatus.ERROR;
          }
        })
        .catch(err => {
          Toast.clear();
          Toast('获取数据失败，请重试');
          console.error('获取用户数据失败:', err);
          this.status = AppStatus.ERROR;
        });
    },
    // 填充表单数据
    fillFormData(data) {
      // 填充基本字段
      if (data.id) {
        this.formData.id = data.id;
      }
      if (data.name) {
        this.formData.name = data.name;
      }
      if (data.idCardNumber) {
        this.formData.idCardNumber = data.idCardNumber;
      }

      // 处理身份证照片
      if (data.idCardImage) {
        const images = data.idCardImage.split(',');
        if (images.length >= 1) {
          this.idCardFront = [images[0]];
        }

        if (images.length >= 2) {
          this.idCardBack = [images[1]];
        }
      }
    },
    handleIdCardFrontUploaded(res) {
      return this.onUploaded(res, 'idCardFront')
    },
    handleIdCardBackUploaded(res) {
      return this.onUploaded(res, 'idCardBack')
    },
    onUploaded(res, field) {
      if (!res || res.data === undefined || res.data === null) return;

      const fieldMap = {
        idCardFront: 'idCardFront',
        idCardBack: 'idCardBack',
      };

      if (fieldMap[field]) {
        this[fieldMap[field]] = [res.data];
      }
      return res.data;
    },
    handleIdCardPreview(img, index, images) {
      this.playPhotos(images, index);
    },
    playPhotos(images, initIndex = 0) {
      const photos = images.map((item, i) => {
        return {
          title: `图片${i}`,
          url: this.imageUrlGetter(item),
        };
      });
      const option = {
        download: true,
        initIndex,
        photos,
      };
      playPhotos(option);
    },
    // 获取图片URL
    imageUrlGetter(resourceId) {
      const baseUrl =
        process.env.NODE_ENV === 'production'
          ? 'https://radio.jgrm.net'
          : 'http://dev.jgrm.net';
      return `${baseUrl}/resource/image/${resourceId}`;
    },
    // 上传图片
    uploadImages() {
      // 此处要执行两个上传
      return Promise.all([
        this.$refs.idCardFrontUploader.submit(),
        this.$refs.idCardBackUploader.submit(),
      ]);
    },
    // 处理身份证图片，将正反面合并
    processIdCardImages() {
      const frontImage = this.idCardFront.length > 0 ? this.idCardFront[0] : '';
      const backImage = this.idCardBack.length > 0 ? this.idCardBack[0] : '';

      // 如果两张图片都存在，返回逗号分隔的字符串，否则返回存在的那一张
      if (frontImage && backImage) {
        return `${frontImage},${backImage}`;
      }
      return frontImage || backImage || '';
    },
    // 验证表单
    validateForm() {
      // 验证姓名
      if (!this.formData.name) {
        Toast('请填写姓名');
        return false;
      }

      // 验证身份证号
      if (!this.formData.idCardNumber) {
        Toast('请填写身份证号');
        return false;
      }

      // 验证身份证号格式
      const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
      if (!idCardRegex.test(this.formData.idCardNumber)) {
        Toast('身份证号格式错误');
        return false;
      }

      // 验证身份证照片
      if (this.idCardFront.length === 0 || this.idCardBack.length === 0) {
        Toast('请上传身份证正反面照片');
        return false;
      }

      return true;
    },
    // 提交表单
    submitForm() {
      if (this.isLoading || this.isBacking) return;
      this.isLoading = true;

      // 先上传图片
      this.uploadImages().then(() => {
        // 验证表单
        if (!this.validateForm()) {
          this.isLoading = false;
          return;
        }

        // 准备提交数据
        const submitData = {
          id: this.formData.id,
          name: this.formData.name,
          idCardNumber: this.formData.idCardNumber,
          idCardImage: this.processIdCardImages(),
        };

        // 提交数据
        updateIdCardInfo(submitData)
          .then(res => {
            Toast('提交成功');
            // 返回上一页
            this.isBacking = true;
            setTimeout(() => {
              this.isBacking = false;
              this.$router.back();
            }, 1500);
          })
          .catch(err => {
            Toast.fail(err.msg || '提交失败');
            console.error('更新身份证信息失败:', err);
          })
          .finally(() => {
            this.isLoading = false;
          });
      });
    },
    onFailed(values) {
      if (values.errors && values.errors.length > 0) {
        Toast(values.errors[0].message);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.update-container {
  padding: 20px 15px;
  background: #f9fafb;
  min-height: 100%;
  box-sizing: border-box;
  padding-bottom: 66px;

  .block-card {
    background: #fff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
  }

  .section-title {
    font-size: 16px;
    font-weight: bold;
    margin: 0px auto 10px;
  }

  .info-tip {
    background-color: #fff7f8;
    color: #f87c98;
    font-size: 14px;
    padding: 10px 16px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    span {
      margin-left: 5px;
    }
  }

  .photo-upload-section {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .photo-label {
    font-size: 14px;
    color: #4b5563;
    margin-bottom: 10px;
  }

  .upload-box {
    width: 100%;
    height: 60px;
    border: 1px dashed #dcdee0;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #f7f8fa;
    color: #6b7280;
    font-size: 12px;
    position: relative;
    box-sizing: border-box;
    padding: 0 10px;
    cursor: pointer;
  }

  .id-card-uploads {
    display: flex;
    justify-content: space-between;

    .uploader-photo {
      width: 48%;
    }
  }

  .id-card-photo ::v-deep {
    width: 48%;
    height: 90px;

    .file-select {
      border: none;
      background: transparent;
      margin: 0;
    }

    .file,
    .file-select {
      width: 100%;
      height: 100%;
    }

    .upload-box {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
    }
  }

  .button-container {
    padding: 20px 0px;
  }

  // 覆盖一些vant默认样式
  ::v-deep .van-field__label {
    width: 80px;
    color: #000000;
    margin-right: 0;

    &::before {
      content: '*';
      color: #ee0a24;
      position: absolute;
      left: 6px;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  ::v-deep .van-field__label:not(.van-field__label--required)::before {
    display: none;
  }

  ::v-deep .van-cell {
    padding: 10px 0 10px 10px;

    &::before {
      left: 0;
    }

    &::after {
      left: 10px;
      right: 0px;
    }
  }

  ::v-deep .van-button--primary {
    background-color: #ff6aa8;
    border-color: #ff6aa8;
  }
}
</style>
