import bus from './bus';
import Events from './events';

/**
 * 选中机油配件
 * @param {object} item 备件信息
 */
export function setPart(item) {
  bus.$emit(Events.AFTER_PART_SELECTED, ...arguments);
}

export function setTransitionBack() {
  bus.$emit(Events.VIEW_TRANSITION_END);
}

/**
 * 选中审车站点
 */
export function selectInspector(item) {
  bus.$emit(Events.AFTER_INSPECTOR_SELECTED, ...arguments);
}

export function onSelectInspector(item) {
  bus.$off(Events.AFTER_INSPECTOR_SELECTED);
  bus.$on(Events.AFTER_INSPECTOR_SELECTED, ...arguments);
}

/**
 * 选中收货地址
 */
export function selectAddress(item) {
  bus.$emit(Events.AFTER_ADDRESS_SELECTED, ...arguments);
}

export function onSelectAddress(item) {
  bus.$off(Events.AFTER_ADDRESS_SELECTED);
  bus.$on(Events.AFTER_ADDRESS_SELECTED, ...arguments);
}

/**
 * 选中代金券
 */
export function selectTicket(item) {
  bus.$emit(Events.AFTER_TICKET_SELECTED, ...arguments);
}

export function onSelectTicket(item) {
  bus.$off(Events.AFTER_TICKET_SELECTED);
  bus.$on(Events.AFTER_TICKET_SELECTED, ...arguments);
}

/**
 * 账户余额支付
 */
export function accountPaySuccess(item) {
  bus.$emit(Events.PAY_SUCCESS, ...arguments);
}

export function onAccountPaySuccess(item) {
  bus.$off(Events.PAY_SUCCESS);
  bus.$on(Events.PAY_SUCCESS, ...arguments);
}

/**
 * 路由跳转，若path是上一级路由的路径，进行 back 操作，否则进行push操作
 * @param {*} path
 */
export function onRouteView(path) {
  bus.$off(Events.ROUTE_VIEW);
  bus.$on(Events.ROUTE_VIEW, ...arguments);
}

export function routeView(path) {
  bus.$emit(Events.ROUTE_VIEW, ...arguments);
}

/**
 * 路由后退，若已经是最后一级，退出webview
 */
export function onRouteBack() {
  bus.$off(Events.ROUTE_BACK);
  bus.$on(Events.ROUTE_BACK, ...arguments);
}

export function routeBack() {
  bus.$emit(Events.ROUTE_BACK, ...arguments);
}

export function fuelcardPrePay() {
  bus.$emit(Events.FUELCARD_PRE_PAY, ...arguments);
}

export function onFuelcardPrePay() {
  bus.$off(Events.FUELCARD_PRE_PAY);
  bus.$on(Events.FUELCARD_PRE_PAY, ...arguments);
}

/**
 * Scroller 组件滚动时
 */
export function scrollerComponentScroll(item) {
  bus.$emit(Events.SCROLLER_SCROLLING, ...arguments);
}

export function onScrollerComponentScroll(item) {
  // bus.$off(Events.SCROLLER_SCROLLING);
  bus.$on(Events.SCROLLER_SCROLLING, ...arguments);
}

export function offScrollerComponentScroll(item) {
  bus.$off(Events.SCROLLER_SCROLLING);
}

/**
 * 显示登录弹窗
 */
export function showLoginDialog(item) {
  bus.$emit(Events.SHOW_LOGIN_DIALOG, ...arguments);
}

export function onShowLoginDialog(item) {
  bus.$off(Events.SHOW_LOGIN_DIALOG);
  bus.$on(Events.SHOW_LOGIN_DIALOG, ...arguments);
}

export function offShowLoginDialog(item) {
  bus.$off(Events.SHOW_LOGIN_DIALOG);
}

/**
 * 登录弹窗登录成功
 */
export function dialogLoginSuccess(item) {
  bus.$emit(Events.DIALOG_LOGIN_SUCCESS, ...arguments);
}

export function onDialogLoginSuccess(item) {
  bus.$off(Events.DIALOG_LOGIN_SUCCESS);
  bus.$on(Events.DIALOG_LOGIN_SUCCESS, ...arguments);
}

export function offDialogLoginSuccess(item) {
  bus.$off(Events.DIALOG_LOGIN_SUCCESS);
}
