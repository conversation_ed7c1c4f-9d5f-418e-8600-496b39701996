<template>
  <container @ready="onReady" @leave="onLeave">
    <x-header title="报名支付">
      <x-button slot="left" type="back"></x-button>
    </x-header>
    <content-view :status="status" @reload="reload">
      <template v-if="status === AppStatus.READY">
        <div class="payment-reminder-container">
          <!-- 支付规则说明 -->
          <div class="title">{{ paymentSettings.name || '报名费用说明' }}</div>
          <div class="payment-rules">
            <BizRichText :value="paymentSettings.description || ''" />
          </div>

          <!-- 确认支付按钮 -->
          <div class="payment-action">
            <!-- 添加价格信息区域 -->
            <div class="price-info">
              <span v-if="paymentSettings.originalPrice > 0" class="original-price">原价: ¥{{
                paymentSettings.originalPrice || 0 }}</span>
              <span class="current-price">优惠价: ¥{{ paymentSettings.needPay || 0 }}</span>
            </div>
            <van-button type="primary" color="#FF4785" block round :loading="loading" loading-text="提交中"
              @click="handlePayment">
              {{ paymentSettings.needPay > 0 ? '确认支付' : '立即报名' }}
            </van-button>
          </div>
        </div>
      </template>
    </content-view>
  </container>
</template>

<script>
import { AppStatus } from '@/enums';
import { mixinAuthRouter } from '@/mixins';
import { Toast, Button } from 'vant';
import { getPaymentSettings, createPaymentRecord } from './api';
import BizRichText from '@/views/components/BizRichText.vue';

export default {
  name: 'PaymentReminder',
  mixins: [mixinAuthRouter],
  components: {
    BizRichText,
    [Toast.name]: Toast,
    [Button.name]: Button,
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      loading: false,
      paymentSettings: {},
    };
  },
  methods: {
    onReady() {
      this.fetchPaymentSettings();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.fetchPaymentSettings();
    },
    fetchPaymentSettings() {
      getPaymentSettings()
        .then(res => {
          this.paymentSettings = res || {};
          this.status = AppStatus.READY;
        })
        .catch(err => {
          Toast(err.message || '获取支付设置失败');
          console.error(err);
          this.status = AppStatus.ERROR;
        });
    },
    handlePayment() {
      if (!this.$_auth_isLoggedIn) {
        this.$_auth_login();
        return;
      }
      if (this.paymentSettings.needPay <= 0) {
        this.$router.push('/blindDate/apply');
        return;
      }
      this.loading = true;
      createPaymentRecord(this.paymentSettings.id)
        .then(res => {
          const { oid, sysOid } = res;
          // 跳转到支付页面
          const paymentInfo = {
            soid: `${sysOid}-${oid}`,
            method: 'replace',
            nextPath: '/blindDate/apply',
          };
          this.$_route_cashierCheckout(paymentInfo);
        })
        .catch(err => {
          Toast(err.message || '创建支付记录失败');
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.payment-reminder-container {
  padding: 15px;
  max-height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .payment-rules {
    flex: 1;
    overflow-y: auto;
    background: #fff;
    padding: 10px;
    border-radius: 8px;
    margin-bottom: 10px;

  }

  .payment-action {
    padding: 10px 0;

    .price-info {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 15px;

      .original-price {
        text-decoration: line-through;
        color: #999;
        margin-right: 15px;
        font-size: 14px;
      }

      .current-price {
        color: #FF4785;
        font-size: 18px;
        font-weight: bold;
      }
    }

    .van-button {
      height: 44px;
      font-size: 16px;
    }
  }
}
</style>
