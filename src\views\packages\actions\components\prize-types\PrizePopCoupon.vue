<template>
  <prize-pop-base :prize="prize" :prizeType="prizeType" :prizeTip="prizeTipText" v-on="$listeners">
    <template #content>
      <div class="prize-count">
        <span class="count">{{ prize.lotteryAmount || prize.amount }}</span>
        <span>元</span>
      </div>
      <div class="coupon-tip">{{ prize.prizeName }}</div>
    </template>
    <template #buttons>
      <van-button class="close-btn" round plain @click="handleCancel">开心收下</van-button>
      <van-button class="" round block type="danger" @click="handleConfirm">立即使用</van-button>
    </template>
  </prize-pop-base>
</template>

<script>
import { PrizeType as PrizeTypeEnum } from '@/enums';
import PrizePopBase from '../PrizePopBase.vue';
import { Button } from 'vant';

export default {
  name: 'PrizePopCoupon',
  components: {
    PrizePopBase,
    [Button.name]: But<PERSON>
  },
  props: {
    prize: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      prizeType: PrizeTypeEnum.COUPON.valueOf()
    };
  },
  computed: {
    prizeTipText() {
      return '优惠券已放入【我的-优惠券】';
    }
  },
  methods: {
    handleConfirm() {
      this.$emit('confirm');
    },
    handleCancel() {
      this.$emit('cancel');
    }
  }
};
</script>

<style lang="scss" scoped>
.prize-count {
  font-size: 12px;
  color: #ff273b;
  text-align: center;
  margin-bottom: 12px;

  .count {
    font-style: italic;
    font-weight: 400;
    font-size: 48px;
    color: #ff273b;
  }

  span:last-child {
    margin-left: 8px;
  }
}

.coupon-tip {
  font-size: 12px;
  color: #ff273b;
  line-height: 14px;
  text-align: center;
  transform: translateY(-10px);
}
</style>
