import { getProfile } from '@/views/blind-date/api';

// 初始状态
const state = {
  profile: {}, // 用户个人资料
  error: null, // 错误信息
};

// 更新状态的方法
const mutations = {
  SET_PROFILE(state, profile) {
    state.profile = profile;
  },
  SET_ERROR(state, error) {
    state.error = error;
  },
  RESET_STATE(state) {
    state.profile = {};
    state.loading = false;
    state.error = null;
  },
};

// 异步操作
const actions = {
  // 获取用户个人资料
  fetchProfile({ commit, state }) {
    return getProfile()
      .then(res => {
        commit('SET_PROFILE', res || {});
        return res || {};
      })
      .catch(err => {
        commit('SET_ERROR', err);
        console.error('获取个人资料失败:', err);
        return Promise.reject(err);
      });
  },

  // 重置状态
  resetState({ commit }) {
    commit('RESET_STATE');
  },
};

// 访问状态的计算属性
const getters = {
  // 获取用户个人资料
  profile: state => state.profile,

  // 获取错误信息
  error: state => state.error,

  // 判断是否有个人资料
  hasProfile: state => Object.keys(state.profile || {}).length > 0,
};

export default {
  namespaced: true, // 使用命名空间
  state,
  mutations,
  actions,
  getters,
};
