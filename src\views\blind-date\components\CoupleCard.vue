<template>
  <div class="couple-card" :class="{ recommend: isRecommendation }" @click="onCardClick">
    <div class="couple-info">
      <!-- 左侧人员信息 -->
      <div class="cp-user female">
        <div class="user-avatar">
          <c-picture class="user-photo" :src="getImageUrl(firstUser.profileImages)" :type="`?imageView2/1/w/480/h/600/format/${supportWebP ? 'webp' : 'jpg'
            }/q/90`">
          </c-picture>
        </div>
        <div class="user-info">
          <div class="user-name-age">
            <span class="user-name">{{ firstUser.nickName }}</span>
            <span class="user-age">{{ firstUser.age }}岁</span>
          </div>
          <div class="user-location">
            {{ firstUser.location || '' }} {{ firstUser.location && firstUser.occupation ? '|' : '' }} {{
              firstUser.occupation || '' }}
          </div>
          <div class="user-details" v-if="showDetails">
            <span>{{ firstUser.height }}cm</span>
          </div>
        </div>
      </div>

      <!-- 中间连接符号 -->
      <div class="cp-heart">
        <img src="@/views/blind-date/images/love.png" alt="匹配" />
      </div>

      <!-- 右侧人员信息 -->
      <div class="cp-user male">
        <div class="user-avatar">
          <c-picture class="user-photo" :src="getImageUrl(secondUser.profileImages)" :type="`?imageView2/1/w/480/h/600/format/${supportWebP ? 'webp' : 'jpg'
            }/q/90`">
          </c-picture>
        </div>
        <div class="user-info">
          <div class="user-name-age">
            <span class="user-name">{{ secondUser.nickName }}</span>
            <span class="user-age">{{ secondUser.age }}岁</span>
          </div>
          <div class="user-location">
            {{ secondUser.location || '' }} {{ secondUser.location && secondUser.occupation ? '|' : '' }} {{
              secondUser.occupation || '' }}
          </div>
          <div class="user-details" v-if="showDetails">
            <span>{{ secondUser.height }}cm</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getImageURL } from '@/common/image';
import { mapState } from 'vuex';

export default {
  name: 'CoupleCard',
  props: {
    // 支持两种数据结构
    // 1. {datingUser1, datingUser2} 结构
    // 2. [user1, user2] 数组结构
    couple: {
      type: [Object, Array],
      required: true
    },
    // 是否显示额外详情（如身高等）
    showDetails: {
      type: Boolean,
      default: false
    },
    // 是否在推荐卡片中显示
    isRecommendation: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState(['supportWebP']),
    // 适配不同的数据结构
    firstUser() {
      if (Array.isArray(this.couple)) {
        return this.couple[0] || {};
      }
      return this.couple.datingUser1 || {};
    },
    secondUser() {
      if (Array.isArray(this.couple)) {
        return this.couple[1] || {};
      }
      return this.couple.datingUser2 || {};
    },
    coupleId() {
      // 对于数组结构，ID可能在第一个用户对象中
      if (Array.isArray(this.couple) && this.couple[0] && this.couple[0].id) {
        return this.couple[0].id;
      }
      // 对于对象结构，ID可能直接在couple对象中
      return this.couple.id;
    }
  },
  methods: {
    getImageUrl(url) {
      if (!url) {
        return '';
      }
      return url.startsWith('http') ? url : url.split(',')[0];
    },
    onCardClick() {
      if (!this.coupleId) return;
      this.$emit('click', this.couple);
    }
  }
};
</script>

<style lang="scss" scoped>
.couple-card {
  background-color: #ffffff;
  border-radius: 10px;
  padding: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  // margin-bottom: 16px;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  }

  &.recommend {
    background: transparent;
    padding: 0;
    box-shadow: none;
    margin-bottom: 0;
  }

  .couple-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
  }

  .cp-user {
    flex: 1;
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .female {
    margin-right: 8px;
  }

  .user-avatar {
    width: 100%;
    height: 180px;
    overflow: hidden;

    .user-photo {
      width: 100%;
      height: 100%;
      display: inline-block;
      border-radius: 12px 12px 0 0;
    }
  }

  .user-info {
    padding: 10px;
    background-color: #fff;
  }

  .user-name-age {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 5px;
  }

  .user-name {
    font-size: 14px;
    font-weight: 500;
    line-height: 21px;
  }

  .user-age {
    font-size: 10.5px;
    font-weight: normal;
    line-height: 14px;
    color: #ff4d6d;
  }

  .user-location {
    font-size: 10.5px;
    font-weight: normal;
    line-height: 14px;
    letter-spacing: normal;
    color: #6b7280;
  }

  .user-details {
    font-size: 10.5px;
    font-weight: normal;
    line-height: 14px;
    color: #6b7280;
    margin-top: 5px;
  }

  .cp-heart {
    width: 60px;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 60px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;

    img {
      display: block;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
