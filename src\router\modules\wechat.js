import { handleError } from '../error-handler';

export default [
  {
    path: '/wechat',
    name: '交广领航微信首页',
    component: resolve => {
      import(
        /* webpackChunkName: "wechat" */ '@/views/packages/wechat/WeChat.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/wechat/me',
    name: '交广领航微信-我的',
    component: resolve => {
      import(
        /* webpackChunkName: "wechat-me" */ '@/views/packages/wechat/WeChatMe.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/activity/result/:activity',
    name: '报名结果',
    component: resolve => {
      import(
        /* webpackChunkName: "activity-result" */ '@/views/packages/wechat/ActivityPayResult.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/wechat/event/orders',
    name: '交广领航微信-我的活动',
    component: resolve => {
      import(
        /* webpackChunkName: "event-orders" */ '@/views/packages/wechat/EventOrders.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/wechat/business/bind',
    name: '交广领航审车商家绑定微信公众号',
    component: resolve => {
      import(
        /* webpackChunkName: "bind-wx-pub" */ '@/views/packages/wechat/BindWxPubVehicle.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
];
