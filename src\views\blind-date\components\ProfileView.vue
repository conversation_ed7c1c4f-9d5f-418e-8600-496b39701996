<template>
  <content-view :status="status" @reload="reload" @scroll-bottom="onScrollBottom">
    <div class="profile-view">
      <div v-if="!$_auth_isLoggedIn" class="login-block">
        <div class="login-tip">登录后查看个人资料</div>
        <van-button type="primary" block @click="login">登录</van-button>
      </div>
      <div v-else-if="
        !userProfile ||
        Object.keys(userProfile || {}).length === 0 ||
        (userProfile && userProfile.status === -1)
      " class="empty-profile">
        <div class="empty-icon">
          <van-icon name="contact" />
        </div>
        <div class="empty-text">
          <h3>您还未创建个人资料</h3>
          <p>填写个人资料后才能参与相亲活动</p>
        </div>
        <div class="empty-action">
          <van-button type="primary" size="large" @click="createProfile">创建我的资料</van-button>
        </div>
      </div>
      <div v-else class="profile-container">
        <div class="profile-header">
          <div class="profile-avatar">
            <img :src="getAvatar(userProfile.profileImages)" alt="用户头像" />
          </div>
          <div class="profile-info">
            <div class="profile-name" @click="toProfile">
              <span>{{ userProfile.nickName }}</span>
              <span v-if="userProfile.status === 3" class="approved-status pending">
                <van-icon name="eye-o" />
                <span>(隐藏资料)</span>
              </span>
              <!-- 展示审核状态approved -->
              <span v-else-if="userProfile.approved" class="approved-status approved">
                <van-icon name="passed" />
                <span>(已审核)</span>
              </span>
              <span v-else class="approved-status pending">
                <van-icon name="clock-o" />
                <span>(待审核)</span>
              </span>
              <!-- <van-icon name="edit" /> -->
            </div>
            <!-- <div class="profile-detail">
            <span>昵称号：{{ userProfile.userCode }}</span>
          </div> -->
            <div class="profile-detail">
              <span>{{ userProfile.age }}岁 {{ userProfile.gender }} </span>
            </div>
          </div>
          <div class="profile-option">
            <div class="visible-btn" @click="toggleProfile">
              <van-icon v-if="userProfile.status === 3" name="closed-eye" />
              <van-icon v-else name="eye-o" />
            </div>
            <div class="edit-profile-btn" @click="toEditProfile">编辑资料</div>
          </div>
        </div>

        <!-- 添加审核拒绝原因展示 -->
        <div v-if="userProfile.status === 2 && userProfile.rejectReason" class="reject-reason">
          <van-icon name="warning-o" />
          <span>审核未通过：{{ userProfile.rejectReason }}</span>
        </div>
        <div class="profile-stats">
          <div class="stats-item">
            <div class="stats-num">{{ userProfile.popularityCount }}</div>
            <div class="stats-text">人气值</div>
          </div>
          <div class="stats-item" @click="continueMatching">
            <div class="stats-icon">
              <van-icon name="like" />
            </div>
            <div class="stats-text">继续寻找</div>
          </div>
        </div>

        <div class="profile-menu">
          <div class="menu-item" @click="goToVerification">
            <div class="menu-text">我的认证</div>
            <div class="menu-right">
              <span class="menu-status">{{
                userProfile.realNameVerified ? '已实名' : '未实名'
                }}</span>
              <van-icon name="arrow" />
            </div>
          </div>

          <div class="menu-item" @click="goToHeartbeat">
            <div class="menu-text">我的心动</div>
            <div class="menu-right">
              <van-icon name="arrow" />
            </div>
          </div>

          <div class="menu-item" @click="goToCouplesMine">
            <div class="menu-text">我的CP</div>
            <div class="menu-right">
              <van-icon name="arrow" />
            </div>
          </div>

          <div class="menu-item" @click="goToLikeMe">
            <div class="menu-text">对我心动</div>
            <div class="menu-right">
              <span v-if="unreadCount" class="count">+{{ unreadCount }}</span>
              <van-icon name="arrow" />
            </div>
          </div>
        </div>

        <!-- <div class="profile-actions">
          <div class="action-button hide-profile" @click="toggleProfile">
            {{ userProfile.status === 3 ? '显示资料' : '隐藏资料' }}
          </div>
        </div> -->

        <!-- 添加底部退出相亲文字链接 -->
        <div class="exit-link-container">
          <span class="exit-link" @click="exitMatching">退出相亲</span>
        </div>

        <!-- <div class="floating-service">
          <van-icon name="service" />
        </div> -->
      </div>
    </div>
  </content-view>
</template>

<script>
import { dialog, toast, loading } from '@/bus';
import { Button, Cell, CellGroup, Toast, Icon, Dialog } from 'vant';
import { mapActions } from 'vuex';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { getImageURL } from '@/common/image';
import { getProfile, hideUser, showUser, exitDating } from '../api';
import ContentView from '@/components/ContentView.vue';
import { AppStatus } from '@/enums';

export default {
  name: 'ProfileView',
  props: {
    unreadCount: {
      type: Number,
      default: 0,
    },
  },
  mixins: [mixinAuthRouter, mixinShare],
  components: {
    [Button.name]: Button,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Icon.name]: Icon,
    [Toast.name]: Toast,
    [Dialog.name]: Dialog,
    ContentView
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      userProfile: {}, // 其中status字段表示用户状态，0:待审核 1:已审核 2:审核拒绝 3:用户隐藏 -1:用户设置退出
    };
  },
  computed: {
    genderText() {
      if (!this.userProfile.gender) return '未设置';
      return this.userProfile.gender === 'male' ? '男' : '女';
    },
  },
  created() {
    if (this.$_auth_isLoggedIn) {
      this.init();
    } else {
      this.$_auth_login();
    }
  },
  methods: {
    ...mapActions('blindDate', ['fetchProfile']),
    refresh() {
      this.init();
    },
    reload() {
      // this.status = AppStatus.LOADING;
      this.init();
      // this.status = AppStatus.READY;
    },
    login() {
      this.$_auth_login();
    },
    init() {
      this.fetchProfile()
        .then(response => {
          this.userProfile = response;
          this.status = AppStatus.READY;
        })
        .catch(error => {
          console.error('获取用户资料失败:', error);
          this.status = AppStatus.ERROR;
        });
    },
    onScrollBottom() {
      // this.$emit('scroll-bottom');
    },
    createProfile() {
      this.$emit('apply');
      // const hasAgreed =
      //   localStorage.getItem('blind_date_agreement_accepted') === 'true';

      // if (hasAgreed) {
      //   // 已同意协议，直接跳转至申请页面
      //   this.$router.push('/blindDate/apply');
      // } else {
      //   // 未同意协议，跳转至协议页面
      //   this.$router.push('/blindDate/agreement');
      // }
    },
    toProfile() {
      // 使用状态-消息映射对象，提高可维护性
      const statusMessages = {
        0: '资料审核中',
        2: '资料审核未通过',
        3: '资料已隐藏',
        '-1': '已退出相亲',
      };

      // 获取当前状态对应的消息（如果存在）
      const status = String(this.userProfile.status);
      const message = statusMessages[status];

      // 如果状态有对应的提示消息，显示提示并返回
      if (message) {
        toast().tip(message);
        return;
      }

      // 状态正常，导航到用户详情页
      this.$_router_push(`/blindDate/user/${this.userProfile.id}`);
    },
    toEditProfile() {
      const path = '/blindDate/apply?id=' + this.userProfile.id;
      this.$_router_push(path);
    },
    goToVerification() {
      const path = '/blindDate/update-id-card';
      this.$_router_push(path);
    },
    goToCouplesMine() {
      const path = '/blindDate/cp';
      this.$_router_push(path);
    },
    goToLikeMe() {
      const path = '/blindDate/likeMe';
      this.$_router_push(path);
    },
    continueMatching() {
      this.$emit('switch-tab', 'HomeView'); // 跳转到首页tab
    },
    goToHeartbeat() {
      const path = '/blindDate/heartbeat';
      this.$_router_push(path);
    },
    toggleProfile() {
      if (this.userProfile.status === 3) {
        this.showProfile();
      } else {
        this.hideProfile();
      }
    },
    hideProfile() {
      Dialog.confirm({
        title: '温馨提示',
        message: '隐藏资料后，您的相亲资料，他人无法查看。确定要隐藏吗？',
        confirmButtonColor: '#f15b5b',
      }).then(() => {
        hideUser({ id: this.userProfile.id })
          .then(() => {
            this.init();
            Toast('已隐藏资料');
          })
          .catch(error => {
            console.error('隐藏资料失败:', error);
          });
      });
    },
    showProfile() {
      Dialog.confirm({
        title: '温馨提示',
        message: '确定要显示您的资料吗？',
        confirmButtonColor: '#f15b5b',
      }).then(() => {
        showUser({ id: this.userProfile.id })
          .then(() => {
            this.init();
            Toast('已显示资料');
          })
          .catch(error => {
            console.error('显示资料失败:', error);
          });
      });
    },
    exitMatching() {
      Dialog.confirm({
        title: '温馨提示',
        message: '退出后，您当前填写的相亲资料将被永久清空，再次使用时需重新填写并等待审核。请确认是否继续退出？',
        confirmButtonColor: '#f15b5b',
      }).then(() => {
        exitDating({ id: this.userProfile.id })
          .then(() => {
            Toast('已退出相亲');
            // 删除用户协议同意标志
            localStorage.removeItem('blind_date_agreement_accepted');
            this.init();
          })
          .catch(error => {
            console.error('退出相亲失败:', error);
          });
      });
    },
    getAvatar(images) {
      if (!images || images.length === 0) return '';
      return getImageURL(images.split(',')[0]);
    },
  },
};
</script>

<style lang="scss" scoped>
.profile-view {
  min-height: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: #ffffff;
  // padding-bottom: 66px;

  // 新增空状态样式
  .empty-profile {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;

    .empty-icon {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background-color: #fff2f2;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 20px;

      .van-icon {
        font-size: 40px;
        color: #f15b5b;
      }
    }

    .empty-text {
      margin-bottom: 30px;

      h3 {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        margin-bottom: 8px;
      }

      p {
        font-size: 14px;
        color: #666;
        margin: 0;
      }
    }

    .empty-action {
      width: 100%;
      max-width: 280px;

      .van-button {
        background-color: #f15b5b;
        border-color: #f15b5b;
      }
    }
  }

  .profile-container {
    height: 100%;
    box-sizing: border-box;
    position: relative;
  }

  .profile-header {
    position: relative;
    background-color: #f15b5b;
    padding: 72px 15px 15px;
    display: flex;
    color: #fff;

    .profile-avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 15px;
      background-color: #fff;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .profile-info {
      flex: 1;

      .profile-name {
        font-size: 18px;
        font-weight: bold;
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        .van-icon {
          margin-left: 5px;
        }

        .approved-status {
          display: inline-flex;
          align-items: center;
          font-size: 12px;
          font-weight: normal;
          padding: 6px 10px;
          border-radius: 12px;
          margin-left: 8px;
          line-height: 1;

          .van-icon {
            margin-right: 3px;
            margin-left: 0;
            font-size: 12px;
          }

          &.approved {
            background-color: #d62d2d;
            color: white;
          }

          &.pending {
            background-color: #f1909f;
            color: white;
          }
        }
      }

      .profile-detail {
        font-size: 14px;
        opacity: 0.9;
      }
    }

    .profile-option {
      position: absolute;
      right: 16px;
      bottom: 15px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }

    .visible-btn {
      position: relative;
      margin-right: 8px;

      &.hide::before {
        content: '——';
        display: inline-block;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%) rotate(140deg);
        font-size: 12px;
        font-weight: bold;
      }
    }

    .van-icon-eye-o {
      font-size: 24px;
      display: inline-block;
      vertical-align: middle;
    }

    .edit-profile-btn {
      background-color: rgba(255, 255, 255, 0.3);
      color: #fff;
      font-size: 14px;
      padding: 6px 14px;
      border-radius: 16px;
    }
  }

  /* 添加审核拒绝原因样式 */
  .reject-reason {
    margin: 8px 15px 0;
    padding: 8px 12px;
    background-color: #fff2f2;
    border-radius: 12px;
    color: #e14040;
    font-size: 14px;
    display: flex;
    align-items: center;
    line-height: 1.4;

    .van-icon {
      margin-right: 5px;
      font-size: 16px;
      flex-shrink: 0;
    }
  }

  .profile-stats {
    display: flex;
    background-color: #ffffff;
    padding: 15px;
    gap: 15px;

    .stats-item {
      flex: 1;
      padding: 15px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-radius: 12px;
      background-color: #fff2f2;

      .stats-num {
        font-size: 26px;
        font-weight: bold;
        color: #f15b5b;
        line-height: 1;
        margin-bottom: 8px;
      }

      .stats-text {
        font-size: 12px;
        color: #999;
      }

      .stats-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #f15b5b;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 5px;

        .van-icon {
          color: #fff;
          font-size: 20px;
        }
      }
    }
  }

  .profile-menu {
    background-color: #fff;

    .menu-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 15px;
      border-bottom: 1px solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .menu-text {
        font-size: 14px;
        color: #000000;
      }

      .menu-right {
        display: flex;
        align-items: center;
        color: #9ca3af;

        .menu-status {
          font-size: 14px;
          margin-right: 5px;
        }

        .van-icon {
          font-size: 16px;
          color: #9ca3af;
        }

        .count {
          font-size: 12px;
          color: #f15b5b;
          line-height: 1;
          display: inline-block;
          margin-right: 5px;
        }
      }
    }
  }

  .profile-actions {
    display: flex;
    padding: 20px 16px;

    .action-button {
      flex: 1;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      font-size: 16px;

      &.hide-profile {
        background-color: #e85b5b;
        color: #fff;
        margin-right: 0;
      }
    }
  }

  // 添加退出相亲文字链接样式
  .exit-link-container {
    text-align: center;
    padding: 20px 0 30px;
    margin-top: 10px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
  }

  .exit-link {
    font-size: 12px;
    color: #c4c4c4;
    text-decoration: none;
    cursor: pointer;
    padding: 5px;
  }

  .floating-service {
    position: fixed;
    right: 20px;
    bottom: 100px;
    width: 42px;
    height: 42px;
    border-radius: 50%;
    background-color: #f15b5b;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(241, 91, 91, 0.4);

    .van-icon {
      color: #fff;
      font-size: 24px;
    }
  }
}
</style>
