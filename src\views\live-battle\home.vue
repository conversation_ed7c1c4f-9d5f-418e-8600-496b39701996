<template>
  <!-- 页面包裹组件 -->
  <container @ready="onReady" @leave="onLeave" @resume="onResume">
    <!-- 头部导航，可根据设计图是否需要决定保留 -->
    <x-header :title="pageTitle">
      <x-button slot="left" type="back"></x-button>
      <x-button slot="right" type="share" @click="share('show')"></x-button>
    </x-header>

    <!-- 页面内容 -->
    <content-view :status="status" @reload="reload">
      <template v-if="status === AppStatus.READY">
        <div class="pk-game">
          <transition name="fade">
            <PlayerCard v-if="showPlayers" @hide="hidePlayers" />
          </transition>

          <!-- 题目区域：动画结束后显示（可做一个淡入或上移等过渡） -->
          <transition name="fade">
            <QuizQuestion v-if="showQuestion" />
          </transition>

          <!-- 结果区域：答题结束后显示 -->
          <transition name="fade">
            <QuizResults v-if="showResults" @share="share('show')" />
          </transition>
        </div>
      </template>
    </content-view>
  </container>
</template>

<script>
import { AppStatus } from '@/enums';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { mixinAuth } from '@/mixins/auth';
import { getImageURL } from '@/common/image';
import { formatDate, getAppURL } from '@/utils';
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex';
import {
  createRoom,
  joinRoom,
  getRoomsList,
  getActionDetail,
} from '@/views/live-battle/api';
import { Toast } from 'vant';
import PlayerCard from './components/PlayerCard';
import QuizQuestion from './components/QuizQuestion';
import QuizResults from './components/QuizResults';

export default {
  name: 'PkGame',
  mixins: [mixinAuthRouter, mixinShare, mixinAuth],
  components: {
    PlayerCard,
    QuizQuestion,
    QuizResults,
    [Toast.name]: Toast,
  },
  data() {
    let actionId = this.$route.query.id;
    let roomId = this.$route.query.roomId;
    let isOwner = this.$route.query.isOwner == '1';
    return {
      AppStatus,
      status: AppStatus.READY,
      pageTitle: '在线答题PK',

      // 动画相关状态
      showPlayers: false, // 是否开始播放两侧选手入场动画
      showQuestion: false, // 是否显示题目区域
      showResults: false, // 是否显示结果区域

      // socket相关
      actionId: actionId,
      roomId, // 15580ee004dc4fd88591264a15925c86
      uid: '',
      serverUrl: '/ws/pk',
      isHouseOwner: isOwner, // 是否是房主
      actionDetail: {},
    };
  },
  computed: {
    ...mapState('quiz', {
      connectionStatus: state => state.connection,
      quizResults: state => state.quiz.results,
      quizStatus: state => state.quiz.status,
      answers: state => state.quiz.answers,
      userId: state => state.user.userId,
      currentQuestionRemainingSeconds: state =>
        state.quiz.currentQuestionRemainingSeconds,
      currentQuestionIndex: state => state.quiz.currentIndex,
    }),
    ...mapGetters('quiz', ['currentQuestion', 'progress']),
    isConnected() {
      return this.connectionStatus.isConnected;
    },
    isConnecting() {
      return this.connectionStatus.isConnecting;
    },
    myScore() {
      if (this.quizResults && this.userId) {
        return this.isHouseOwner
          ? this.quizResults.promoter.score
          : this.quizResults.opponent.score;
      }
      return 0;
    },
    opponentScore() {
      if (this.quizResults && this.userId) {
        return this.isHouseOwner
          ? this.quizResults.opponent.score
          : this.quizResults.promoter.score;
      }
      return 0;
    },
    connectionError() {
      return this.connectionStatus.error;
    },
    connectionStatusClass() {
      if (this.connectionError) {
        return 'alert-danger';
      }
      return this.isConnected ? 'alert-success' : 'alert-warning';
    },
    connectionStatusMessage() {
      if (this.connectionError) {
        return `连接错误: ${this.connectionError}`;
      }
      if (this.isConnecting) {
        return '正在连接到服务器...';
      }
      return this.isConnected ? '已连接到服务器' : '未连接到服务器';
    },
    showConnectionStatus() {
      return this.isConnecting || this.connectionError || this.isConnected;
    },
  },
  watch: {
    quizStatus(val) {
      if (val === 'ended') {
        this.roomId = '';
        this.isHouseOwner = false;
        this.showPlayers = false;
        this.showQuestion = this.answers.length > 0;
        this.showResults = true;
      }
    },
    currentQuestionIndex(val) {
      if (val > 1) {
        this.hidePlayers();
      }
    },
  },
  mounted() {
    // this.init();
  },
  beforeRouteUpdate(to, from, next) {
    if (to.path === '/live/battle/room') {
      if (to.query.isOwner === '1') {
        this.isHouseOwner = true;
      }
      if (to.query.roomId) {
        this.roomId = to.query.roomId;
      }
      this.showPlayers = true;
      this.showQuestion = false;
      this.showResults = false;
      this.disconnect().then(() => {
        this.init();
      });
    }
    next();
  },
  beforeRouteLeave(to, from, next) {
    this.disconnect();
    next();
  },
  methods: {
    ...mapActions('quiz', [
      'connect',
      'disconnect',
      'startQuiz',
      'submitAnswer',
    ]),
    ...mapMutations('quiz', ['SET_USER']),
    onReady() {
      this.init();
    },
    onLeave() {
      this.disconnect();
    },
    onResume() {
      // this.init();
    },
    async init() {
      try {
        this.uid = this.$_auth_userInfo.uid || '';
        // 如果已有房间ID，直接加入
        if (this.roomId) {
          return this.joinQuiz();
        }

        // 获取房间列表并过滤掉自己创建的房间
        const rooms = (await getRoomsList({ actionId: this.actionId })) || [];
        const availableRooms = rooms.filter(r => r.user1Id !== this.uid);

        // 路由参数
        const routeParams = {
          path: '/live/battle/room',
          query: {
            id: this.actionId,
            roomId: '',
          },
        };

        if (availableRooms.length > 0) {
          // 加入现有房间
          this.roomId = availableRooms[0].id;
          routeParams.query.roomId = this.roomId;
        } else {
          // 创建新房间
          const room = await this.createRoom();
          routeParams.query = {
            ...routeParams.query,
            roomId: this.roomId,
            isOwner: 1,
          };
        }

        // 路由跳转
        await this.$router.replace(routeParams);
      } catch (error) {
        console.error('初始化房间失败:', error);
        Toast('初始化房间失败，请重试');
      }
    },
    joinQuiz() {
      if (!this.actionId || !this.serverUrl || !this.uid) {
        Toast('请填写活动ID、服务器地址和用户ID');
        return;
      }

      if (this.currentQuestionIndex > 1) {
        this.showPlayers = false;
        this.showQuestion = true;
      } else {
        this.showPlayers = true;
        this.showQuestion = false;
      }
      // 设置用户信息
      this.$store.commit('quiz/SET_USER', {
        name: this.roomId,
        roomId: this.roomId,
        userId: this.uid,
        isHouseOwner: this.isHouseOwner,
      });

      // 连接到服务器
      this.connect(this.serverUrl);
      this.getActionDetail();
    },
    createRoom() {
      const actionId = this.actionId;
      const uid = this.uid;
      return createRoom({
        actionId,
        uid,
      }).then(room => {
        this.isHouseOwner = true;
        this.roomId = room.id;
      });
    },
    joinRoom() {
      const roomId = this.roomId;
      const uid = this.uid;
      joinRoom(roomId, {
        uid,
      }).then(room => {});
    },
    handleAnswerSubmit(answer) {
      this.submitAnswer(answer);
    },
    reload() {
      this.init();
    },
    hidePlayers() {
      this.showPlayers = false;
      setTimeout(() => {
        if (this.quizStatus === 'waiting') {
          this.startQuiz();
          this.showQuestion = true;
        }
      }, 300); // 300毫秒后显示问题组件，可以根据您的淡出动画持续时间调整
      // this.showQuestion = true;
    },
    // 点击选项
    selectOption(opt) {
      // 选择答案后的逻辑
      this.$toast(`你选择了：${opt}`);
    },
    getActionDetail() {
      if (!this.actionId) return;
      getActionDetail(this.actionId).then(res => {
        this.actionDetail = res.action;
        this.pageTitle = this.actionDetail.title;
        this.share();
      });
    },
    // 分享
    share(action = 'config') {
      const title = this.actionDetail.title;
      const logo = getImageURL(this.actionDetail.shareImage);
      // const desc = this.actionDetail.description;
      const link = '/live/battle/home?id=' + this.actionId;
      const url = getAppURL(link);
      const shareInfo = {
        title: title || '交广领航在线答题PK，展示你的知识才华！',
        desc: title || '交广领航在线答题PK，展示你的知识才华！',
        imgUrl: logo,
        link: url,
      };
      if (action === 'show') {
        this.$_share(shareInfo);
      } else {
        this.$_share_update(shareInfo);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.pk-game {
  position: relative;
  width: 100%;
  height: 100%;
  // overflow: hidden; // 防止溢出
  background: #5944ff;

  // 题目区域
  .question-area {
    position: absolute;
    bottom: 60px;
    left: 50%;
    transform: translateX(-50%);
    width: 300px; // 可根据设计图调整
    padding: 16px;
    background: #fff;
    border-radius: 12px;
    text-align: center;
    z-index: 4;

    .top-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
      .time-box {
        font-size: 14px;
        color: #ff4d4f;
      }
      .question-progress {
        font-size: 14px;
        color: #666;
      }
    }
    .question-content {
      .question-title {
        font-size: 16px;
        color: #333;
        margin-bottom: 16px;
      }
      .options {
        .option {
          padding: 10px;
          margin: 8px 0;
          border: 1px solid #d9d9d9;
          border-radius: 8px;
          font-size: 14px;
          color: #333;
          cursor: pointer;
          &:hover {
            background-color: #f5f5f5;
          }
        }
      }
    }
  }
}

/* 题目淡入上移过渡 (transition + fade-up) */
.fade-up-enter-active,
.fade-up-leave-active {
  transition: all 0.6s ease;
}
.fade-up-enter {
  opacity: 0;
  transform: translateX(-50%) translateY(50px);
}
.fade-up-enter-to {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}
</style>
