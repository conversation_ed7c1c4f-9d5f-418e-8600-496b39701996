<template>
  <div v-if="recommendations.id" class="cp-recommendation">
    <div class="cp-header">
      <div class="cp-title">CP推荐</div>
      <div class="cp-more" @click="viewMore">更多</div>
    </div>
    <div class="cp-content">
      <couple-card :couple="recommendations" :is-recommendation="true" @click="goToCPDetail" />
    </div>
  </div>
</template>

<script>
import { getImageURL } from '@/common/image';
import CoupleCard from './CoupleCard.vue';

export default {
  name: 'CPRecommendation',
  components: {
    CoupleCard
  },
  props: {
    recommendations: {
      type: [Object, Array],
      default: () => { },
    }
  },
  methods: {
    goToCPDetail(couple) {
      if (!couple.id) return;
      this.$router.push({
        path: `/blindDate/couple/${couple.id}`,
      });
    },
    viewMore() {
      this.$emit('more'); // 跳转到CP列表
    },
    getImageUrl(url) {
      if (!url) {
        return '';
      }
      return url.startsWith('http') ? url : getImageURL(url.split(',')[0]);
    },
  }
};
</script>

<style lang="scss" scoped>
.cp-recommendation {
  background: linear-gradient(270deg, #A990F4 0%, #D592F5 100%);
  border-radius: 12px;
  padding: 8px;
  margin-top: 20px;
}

.cp-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.cp-title {
  font-size: 16px;
  font-weight: 500;
  color: #FFFFFF;
}

.cp-more {
  font-size: 12px;
  color: #FFFFFF;
}

.cp-content {
  width: 100%;
}
</style>
