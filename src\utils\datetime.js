import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

dayjs.locale('zh-cn');

/**
 * 返回现在到传入时间的相对时间。
 * 时间划分：
 * 5分钟前返回刚刚
 * 1小时内按照分钟计算，如15分钟前
 * 超过1小时按照小时计算，如用户在1小时20分前浏览该页面，展示为1小时前。
 * 展示最长时间维度为24小时，如用户在25小时20分前浏览该页面，展示为24小时前
 * @param {number} t 毫秒数
 */
export function formatTime(timestamp) {
  if (!timestamp) return '';
  const now = dayjs();
  const diffMinutes = now.diff(timestamp, 'minute');
  const diffHours = now.diff(timestamp, 'hour');

  if (diffMinutes < 5) {
    return '刚刚';
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else {
    return '24小时前';
  }
}

/**
 * 时间段分解为时分秒等
 * @param {number} t 毫秒数
 */
export function getDuration(t) {
  const total = t;
  const [DAY, HOUR, MINUTE, SECOND] = [60 * 60 * 24, 60 * 60, 60, 1].map(
    value => value * 1000
  );

  let remain = 0;

  // 天
  const days = parseInt(total / DAY, 10);
  remain = total - days * DAY;

  // 小时
  const hours = parseInt(remain / HOUR, 10);
  remain = remain - hours * HOUR;

  // 分
  const minutes = parseInt(remain / MINUTE);
  remain = remain - minutes * MINUTE;

  // 秒
  const seconds = parseInt(remain / SECOND, 10);
  remain = remain - seconds * SECOND;

  // 毫秒
  const mseconds = parseInt(remain, 10);

  return {
    hours: () => hours,
    minutes: () => minutes,
    seconds: () => seconds,
    days: () => days,
    $d: days,
    $h: hours,
    $m: minutes,
    $s: seconds,
    $ms: mseconds,
    $differ: total,
  };
}

export function formatDate(t, fmt = 'YYYY-MM-DD') {
  const date = new Date(Number(t));
  if (!date.getTime()) return t;
  let o = {
    'M+': date.getMonth() + 1,
    '[Dd]+': date.getDate(),
    '[hH]+': date.getHours(),
    '[m]+': date.getMinutes(),
    '[wW]': '日一二三四五六'.split('')[date.getDay()],
    's+': date.getSeconds(),
    'q+': Math.floor((date.getMonth() + 3) / 3),
    S: date.getMilliseconds(),
  };
  if (/([Yy]+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (date.getFullYear() + '').substr(4 - RegExp.$1.length)
    );
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      );
    }
  }
  return fmt;
}

export function getFullURL(path) {
  const link = document.createElement('a');
  link.href = path;
  return link.href;
}

export function formatDistance(distance) {
  if (distance == null) {
    return '';
  }
  const MIN = 100;
  const m = parseInt(distance);
  if (m <= MIN) return `<${MIN}m`;
  if (m < 1000) return `${m}m`;
  if (m > 9999 * 1000) return '>9999km';
  return (m / 1000).toFixed(m > 99 * 1000 ? 0 : 1) + 'km';
}

export function formatShopHours(hour) {
  const LENGTH = 4;
  let result = String(hour);
  const padLength = LENGTH - result.length;
  if (padLength > 0) {
    result =
      Array.from(Array(padLength))
        .map(item => 0)
        .join('') + result;
  }
  return result.replace(/(\d{2})$/, ':$1');
}

export function formatPrice(value) {
  if (!value) return 0;
  return Number(value.toFixed(2));
}

export function parseAppointmentTime(value) {
  const regex = /^(\d{4})(\d{2})(\d{2})(\d)$/;
  const result = regex.exec(value);
  if (result && result.length == 5) {
    let [all, year, month, day, meridian] = result;
    return `${year}-${month}-${day} ${meridian == 1 ? '下午' : '上午'}`;
  }
  return value;
}

export function zeroFill(number, fix = 2) {
  const chars = String(number).split('');
  while (chars.length < fix) {
    chars.unshift(0);
  }
  return chars.join('');
}

/**
 * 获取任意时间
 */
export function getDate(date, AddDayCount = 0, str = 'day') {
  if (!date) {
    date = new Date();
  }
  if (typeof date !== 'object') {
    date = date.replace(/-/g, '/');
  }
  const dd = new Date(date);
  switch (str) {
    case 'day':
      dd.setDate(dd.getDate() + AddDayCount); // 获取AddDayCount天后的日期
      break;
    case 'month':
      if (dd.getDate() === 31) {
        dd.setDate(dd.getDate() + AddDayCount);
      } else {
        dd.setMonth(dd.getMonth() + AddDayCount); // 获取AddDayCount天后的日期
      }
      break;
    case 'year':
      dd.setFullYear(dd.getFullYear() + AddDayCount); // 获取AddDayCount天后的日期
      break;
  }
  const y = dd.getFullYear();
  const m =
    dd.getMonth() + 1 < 10 ? '0' + (dd.getMonth() + 1) : dd.getMonth() + 1; // 获取当前月份的日期，不足10补0
  const d = dd.getDate() < 10 ? '0' + dd.getDate() : dd.getDate(); // 获取当前几号，不足10补0
  return {
    fullDate: y + '-' + m + '-' + d,
    year: y,
    month: m,
    date: d,
    day: dd.getDay(),
  };
}
/**
 * 获取日期范围内所有日期
 * @param {String} begin 开始时间
 * @param {String} end 结束时间
 * @param {String} delimiter 分隔符 '-,/'
 * @return {Array} ['2022-11-22', '2022-11-22']
 */
export function getDateAll(begin, end, delimiter = '-') {
  let arr = [];
  let ab = begin.split(delimiter);
  let ae = end.split(delimiter);
  let db = new Date();
  db.setFullYear(ab[0], ab[1] - 1, ab[2]);
  let de = new Date();
  de.setFullYear(ae[0], ae[1] - 1, ae[2]);
  let unixDb = db.getTime() - 24 * 60 * 60 * 1000;
  let unixDe = de.getTime() - 24 * 60 * 60 * 1000;
  // prettier-ignore
  for (let k = unixDb; k <= unixDe;) {
    k = k + 24 * 60 * 60 * 1000;
    arr.push(getDate(new Date(parseInt(k))).fullDate);
  }
  return arr;
}
/**
 * @description: 比较时间大小
 * @param {String} startDate
 * @param {String} endDate
 * @return {Boolean} startDate <= endDate = true
 */
export function dateCompare(startDate, endDate) {
  // 计算截止时间
  startDate = new Date(startDate.replace('-', '/').replace('-', '/'));
  // 计算详细项的截止时间
  endDate = new Date(endDate.replace('-', '/').replace('-', '/'));
  if (startDate <= endDate) {
    return true;
  } else {
    return false;
  }
}

/**
 * 获取当前周的开始时间和结束时间
 * @param {string} format 返回的日期格式，默认为'YYYY-MM-DD'
 * @param {number} startOfWeek 一周的开始是周几，0代表周日，1代表周一，默认为1
 * @returns {Object} 包含start和end属性的对象
 */
export function getWeekRange(format = 'YYYY-MM-DD', startOfWeek = 1) {
  // 由于dayjs的startOf('week')默认是周日开始，
  // 如果需要设置周一为开始，需要手动计算
  const now = dayjs();
  let weekStart, weekEnd;

  if (startOfWeek === 1) {
    // 如果周一为开始日
    const day = now.day();
    const diffStart = day === 0 ? -6 : 1 - day; // 周日特殊处理
    weekStart = now.add(diffStart, 'day').startOf('day');
    weekEnd = weekStart.add(6, 'day').endOf('day');
  } else {
    // 如果周日为开始日（dayjs默认）
    weekStart = now.startOf('week');
    weekEnd = now.endOf('week');
  }

  return {
    start: weekStart.format(format),
    end: weekEnd.format(format),
  };
}

/**
 * 获取当前月的开始时间和结束时间
 * @param {string} format 返回的日期格式，默认为'YYYY-MM-DD'
 * @returns {Object} 包含start和end属性的对象
 */
export function getMonthRange(format = 'YYYY-MM-DD') {
  const now = dayjs();
  const monthStart = now.startOf('month').format(format);
  const monthEnd = now.endOf('month').format(format);

  return {
    start: monthStart,
    end: monthEnd,
  };
}

/**
 * 获取当前周和当前月的时间范围
 * @param {string} format 返回的日期格式，默认为'YYYY-MM-DD'
 * @param {number} startOfWeek 一周的开始是周几，0代表周日，1代表周一，默认为1
 * @returns {Object} 包含week和month属性的对象，每个属性又包含start和end
 *
 * @example
 * // 在Vue组件中使用
 * import { getDateRanges } from '@/utils/datetime';
 *
 * export default {
 *   data() {
 *     return {
 *       data: {
 *         dateRanges: getDateRanges()
 *       }
 *     }
 *   },
 *   methods: {
 *     fetchData() {
 *       // 使用当前周的开始和结束时间
 *       const { start: weekStart, end: weekEnd } = this.data.dateRanges.week;
 *       // 或使用当前月的开始和结束时间
 *       const { start: monthStart, end: monthEnd } = this.data.dateRanges.month;
 *
 *       // 在API请求中使用
 *       this.$api.getData({
 *         startDate: weekStart,
 *         endDate: weekEnd
 *       });
 *     }
 *   }
 * }
 *
 */
export function getDateRanges(format = 'YYYY-MM-DD', startOfWeek = 1) {
  return {
    week: getWeekRange(format, startOfWeek),
    month: getMonthRange(format),
  };
}
